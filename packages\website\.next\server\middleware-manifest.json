{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_64d8cd1e._.js", "server/edge/chunks/[root-of-the-server]__ee6b1bdb._.js", "server/edge/chunks/turbopack-packages_website_edge-wrapper_d0b1f80c.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next|_vercel|.*\\..*).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next|_vercel|.*\\..*).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "afg8pKhnCsgeS22ryFG1dd5x9bVj9fneOlAn7UPDJog=", "__NEXT_PREVIEW_MODE_ID": "898546e45d011c638b1879bd1376f605", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "6969c621822e311ec5304c1aaf4b00b8c722b30af37d6fb135db9fdeac673810", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "e33ff40b50d580bd52d993374a4ccf1ee98ef3008e9f722419dfd63a3685610f"}}}, "sortedMiddleware": ["/"], "functions": {}}