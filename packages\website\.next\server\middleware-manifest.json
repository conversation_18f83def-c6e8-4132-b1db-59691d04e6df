{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_64d8cd1e._.js", "server/edge/chunks/[root-of-the-server]__ee6b1bdb._.js", "server/edge/chunks/turbopack-packages_website_edge-wrapper_d0b1f80c.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next|_vercel|.*\\..*).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next|_vercel|.*\\..*).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "afg8pKhnCsgeS22ryFG1dd5x9bVj9fneOlAn7UPDJog=", "__NEXT_PREVIEW_MODE_ID": "aead6ef432990fc72002538c02e02496", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7dff445b168de1c88701e22ee503897c1152679e17f61a459fec98b77528ec57", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "608e3a822646b2df41b86cda1594de435b8c35bcbba6537903c48d4d3121ad4e"}}}, "sortedMiddleware": ["/"], "functions": {}}