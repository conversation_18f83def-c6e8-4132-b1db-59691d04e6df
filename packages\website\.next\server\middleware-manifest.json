{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_64d8cd1e._.js", "server/edge/chunks/[root-of-the-server]__ee6b1bdb._.js", "server/edge/chunks/turbopack-packages_website_edge-wrapper_d0b1f80c.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next|_vercel|.*\\..*).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next|_vercel|.*\\..*).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "afg8pKhnCsgeS22ryFG1dd5x9bVj9fneOlAn7UPDJog=", "__NEXT_PREVIEW_MODE_ID": "ce836134568966154fac50ef87982f13", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "71fa4459a39b77cc9422940023a56171832ba73ee7b299e9d5c9b11c6bffc8f2", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "9304284dfab1f37bdd9a76938b38857da808d5e39f5273032b22d5a3ff537eef"}}}, "sortedMiddleware": ["/"], "functions": {}}