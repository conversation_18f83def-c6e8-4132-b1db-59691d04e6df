{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/webs/apiflow/packages/website/src/components/sections/HeroSection.tsx"], "sourcesContent": ["import Link from 'next/link';\nimport { ArrowR<PERSON>, Star, Users, Zap } from 'lucide-react';\nimport { useTranslations } from 'next-intl';\n\nexport default function HeroSection() {\n  const t = useTranslations('hero');\n  const stats = [\n    { icon: Users, value: '10K+', label: t('stats.developers') },\n    { icon: Star, value: '4.9/5', label: t('stats.rating') },\n    { icon: Zap, value: '99.9%', label: t('stats.uptime') },\n  ];\n\n  return (\n    <section className=\"relative pt-20 pb-16 sm:pt-24 sm:pb-20 lg:pt-32 lg:pb-28\">\n      {/* Background gradient */}\n      <div className=\"absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-purple-50\"></div>\n      \n      <div className=\"relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center\">\n          {/* Badge */}\n          <div className=\"inline-flex items-center rounded-full bg-blue-100 px-4 py-2 text-sm font-medium text-blue-800 mb-8\">\n            {t('badge')}\n          </div>\n\n          {/* Main heading */}\n          <h1 className=\"text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl lg:text-6xl\">\n            {t('title')}{' '}\n            <span className=\"gradient-text\">{t('titleHighlight')}</span>\n          </h1>\n\n          {/* Subheading */}\n          <p className=\"mx-auto mt-6 max-w-3xl text-lg leading-8 text-gray-600 sm:text-xl\">\n            {t('subtitle')}\n          </p>\n\n          {/* CTA Buttons */}\n          <div className=\"mt-10 flex flex-col sm:flex-row items-center justify-center gap-4\">\n            <Link\n              href=\"https://github.com/trueleaf/apiflow\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"inline-flex items-center rounded-lg bg-blue-600 px-8 py-3 text-base font-semibold text-white shadow-lg hover:bg-blue-700 transition-all duration-200 hover:scale-105\"\n            >\n              {t('startFreeTrial')}\n              <ArrowRight className=\"ml-2 h-5 w-5\" />\n            </Link>\n          </div>\n\n          {/* Stats */}\n          <div className=\"mt-16 grid grid-cols-1 gap-8 sm:grid-cols-3 lg:gap-16\">\n            {stats.map((stat, index) => {\n              const Icon = stat.icon;\n              return (\n                <div key={index} className=\"text-center\">\n                  <div className=\"mx-auto flex h-12 w-12 items-center justify-center rounded-lg bg-blue-100\">\n                    <Icon className=\"h-6 w-6 text-blue-600\" />\n                  </div>\n                  <div className=\"mt-4\">\n                    <div className=\"text-2xl font-bold text-gray-900\">{stat.value}</div>\n                    <div className=\"text-sm text-gray-600\">{stat.label}</div>\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;AAAA;AAAA;AACA;;;;;AAEe,SAAS;IACtB,MAAM,IAAI,IAAA,iQAAe,EAAC;IAC1B,MAAM,QAAQ;QACZ;YAAE,MAAM,6MAAK;YAAE,OAAO;YAAQ,OAAO,EAAE;QAAoB;QAC3D;YAAE,MAAM,0MAAI;YAAE,OAAO;YAAS,OAAO,EAAE;QAAgB;QACvD;YAAE,MAAM,uMAAG;YAAE,OAAO;YAAS,OAAO,EAAE;QAAgB;KACvD;IAED,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;;;;;0BAEf,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACZ,EAAE;;;;;;sCAIL,8OAAC;4BAAG,WAAU;;gCACX,EAAE;gCAAU;8CACb,8OAAC;oCAAK,WAAU;8CAAiB,EAAE;;;;;;;;;;;;sCAIrC,8OAAC;4BAAE,WAAU;sCACV,EAAE;;;;;;sCAIL,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,uKAAI;gCACH,MAAK;gCACL,QAAO;gCACP,KAAI;gCACJ,WAAU;;oCAET,EAAE;kDACH,8OAAC,gOAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAK1B,8OAAC;4BAAI,WAAU;sCACZ,MAAM,GAAG,CAAC,CAAC,MAAM;gCAChB,MAAM,OAAO,KAAK,IAAI;gCACtB,qBACE,8OAAC;oCAAgB,WAAU;;sDACzB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;;;;;;;;;;;sDAElB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAoC,KAAK,KAAK;;;;;;8DAC7D,8OAAC;oDAAI,WAAU;8DAAyB,KAAK,KAAK;;;;;;;;;;;;;mCAN5C;;;;;4BAUd;;;;;;;;;;;;;;;;;;;;;;;AAMZ", "debugId": null}}, {"offset": {"line": 214, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/webs/apiflow/packages/website/src/components/sections/ComparisonSection.tsx"], "sourcesContent": ["import React from 'react';\nimport { Check, X, Zap, Shield, Users, Code } from 'lucide-react';\nimport { useTranslations } from 'next-intl';\n\nexport default function ComparisonSection() {\n  const t = useTranslations('comparison');\n  const features = [\n    {\n      category: 'Core Features',\n      items: [\n        { feature: 'API Testing & Documentation', apiflow: true, postman: true, apifox: true },\n        { feature: 'Real-time Collaboration', apiflow: true, postman: 'limited', apifox: true },\n        { feature: 'Advanced Mock Servers', apiflow: true, postman: 'paid', apifox: true },\n        { feature: 'Automated Testing Workflows', apiflow: true, postman: 'paid', apifox: 'limited' },\n        { feature: 'GraphQL Support', apiflow: true, postman: true, apifox: true },\n      ]\n    },\n    {\n      category: 'Developer Experience',\n      items: [\n        { feature: 'Intuitive Interface', apiflow: true, postman: 'good', apifox: 'good' },\n        { feature: 'Fast Performance', apiflow: true, postman: 'slow', apifox: 'good' },\n        { feature: 'Offline Mode', apiflow: true, postman: true, apifox: 'limited' },\n        { feature: 'Custom Themes', apiflow: true, postman: false, apifox: 'limited' },\n        { feature: 'Plugin Ecosystem', apiflow: true, postman: true, apifox: 'limited' },\n      ]\n    },\n    {\n      category: 'Enterprise',\n      items: [\n        { feature: 'SSO Integration', apiflow: true, postman: 'paid', apifox: 'paid' },\n        { feature: 'Advanced Security', apiflow: true, postman: 'paid', apifox: 'limited' },\n        { feature: 'Custom Deployment', apiflow: true, postman: 'enterprise', apifox: false },\n        { feature: 'Priority Support', apiflow: true, postman: 'paid', apifox: 'paid' },\n        { feature: 'Compliance Ready', apiflow: true, postman: 'enterprise', apifox: 'limited' },\n      ]\n    }\n  ];\n\n  const renderFeatureValue = (value: boolean | string) => {\n    if (value === true) {\n      return <Check className=\"h-5 w-5 text-green-600\" />;\n    } else if (value === false) {\n      return <X className=\"h-5 w-5 text-red-500\" />;\n    } else {\n      return <span className=\"text-xs text-yellow-600 font-medium\">{value}</span>;\n    }\n  };\n\n  const highlights = [\n    {\n      icon: Zap,\n      title: '3x Faster',\n      description: 'API testing workflows compared to traditional tools'\n    },\n    {\n      icon: Shield,\n      title: 'Enterprise Ready',\n      description: 'Built-in security and compliance features from day one'\n    },\n    {\n      icon: Users,\n      title: 'Team Focused',\n      description: 'Real-time collaboration without the premium price tag'\n    },\n    {\n      icon: Code,\n      title: 'Developer First',\n      description: 'Designed by developers, for developers'\n    }\n  ];\n\n  return (\n    <section id=\"features\" className=\"py-16 sm:py-20 lg:py-24 bg-gray-50\">\n      <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        {/* Section header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl lg:text-5xl\">\n            Why Choose APIFlow?\n          </h2>\n          <p className=\"mx-auto mt-6 max-w-3xl text-lg text-gray-600\">\n            See how APIFlow compares to other popular API tools. We've built everything \n            you love about existing tools, plus the features you've been waiting for.\n          </p>\n        </div>\n\n        {/* Highlights */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16\">\n          {highlights.map((highlight, index) => {\n            const Icon = highlight.icon;\n            return (\n              <div key={index} className=\"text-center\">\n                <div className=\"mx-auto flex h-16 w-16 items-center justify-center rounded-xl bg-blue-600 mb-4\">\n                  <Icon className=\"h-8 w-8 text-white\" />\n                </div>\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">{highlight.title}</h3>\n                <p className=\"text-gray-600\">{highlight.description}</p>\n              </div>\n            );\n          })}\n        </div>\n\n        {/* Comparison table */}\n        <div className=\"bg-white rounded-2xl shadow-xl overflow-hidden\">\n          <div className=\"px-6 py-8 bg-gradient-to-r from-blue-600 to-purple-600\">\n            <h3 className=\"text-2xl font-bold text-white text-center\">\n              Feature Comparison\n            </h3>\n            <p className=\"text-blue-100 text-center mt-2\">\n              See how we stack up against the competition\n            </p>\n          </div>\n\n          <div className=\"overflow-x-auto\">\n            <table className=\"w-full\">\n              <thead className=\"bg-gray-50\">\n                <tr>\n                  <th className=\"px-6 py-4 text-left text-sm font-semibold text-gray-900\">\n                    Feature\n                  </th>\n                  <th className=\"px-6 py-4 text-center text-sm font-semibold text-blue-600\">\n                    APIFlow\n                  </th>\n                  <th className=\"px-6 py-4 text-center text-sm font-semibold text-gray-600\">\n                    Postman\n                  </th>\n                  <th className=\"px-6 py-4 text-center text-sm font-semibold text-gray-600\">\n                    Apifox\n                  </th>\n                </tr>\n              </thead>\n              <tbody className=\"divide-y divide-gray-200\">\n                {features.map((category, categoryIndex) => (\n                  <React.Fragment key={`category-group-${categoryIndex}`}>\n                    <tr key={`category-${categoryIndex}`} className=\"bg-gray-50\">\n                      <td colSpan={4} className=\"px-6 py-3 text-sm font-semibold text-gray-700\">\n                        {category.category}\n                      </td>\n                    </tr>\n                    {category.items.map((item, itemIndex) => (\n                      <tr key={`${categoryIndex}-${itemIndex}`} className=\"hover:bg-gray-50\">\n                        <td className=\"px-6 py-4 text-sm text-gray-900\">\n                          {item.feature}\n                        </td>\n                        <td className=\"px-6 py-4 text-center\">\n                          {renderFeatureValue(item.apiflow)}\n                        </td>\n                        <td className=\"px-6 py-4 text-center\">\n                          {renderFeatureValue(item.postman)}\n                        </td>\n                        <td className=\"px-6 py-4 text-center\">\n                          {renderFeatureValue(item.apifox)}\n                        </td>\n                      </tr>\n                    ))}\n                  </React.Fragment>\n                ))}\n              </tbody>\n            </table>\n          </div>\n\n          <div className=\"px-6 py-6 bg-blue-50 text-center\">\n            <p className=\"text-sm text-gray-600 mb-4\">\n              Ready to experience the difference?\n            </p>\n            <button className=\"inline-flex items-center rounded-lg bg-blue-600 px-6 py-3 text-base font-semibold text-white shadow-lg hover:bg-blue-700 transition-all duration-200\">\n              Start Your Free Trial\n            </button>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;;;AAEe,SAAS;IACtB,MAAM,IAAI,IAAA,iQAAe,EAAC;IAC1B,MAAM,WAAW;QACf;YACE,UAAU;YACV,OAAO;gBACL;oBAAE,SAAS;oBAA+B,SAAS;oBAAM,SAAS;oBAAM,QAAQ;gBAAK;gBACrF;oBAAE,SAAS;oBAA2B,SAAS;oBAAM,SAAS;oBAAW,QAAQ;gBAAK;gBACtF;oBAAE,SAAS;oBAAyB,SAAS;oBAAM,SAAS;oBAAQ,QAAQ;gBAAK;gBACjF;oBAAE,SAAS;oBAA+B,SAAS;oBAAM,SAAS;oBAAQ,QAAQ;gBAAU;gBAC5F;oBAAE,SAAS;oBAAmB,SAAS;oBAAM,SAAS;oBAAM,QAAQ;gBAAK;aAC1E;QACH;QACA;YACE,UAAU;YACV,OAAO;gBACL;oBAAE,SAAS;oBAAuB,SAAS;oBAAM,SAAS;oBAAQ,QAAQ;gBAAO;gBACjF;oBAAE,SAAS;oBAAoB,SAAS;oBAAM,SAAS;oBAAQ,QAAQ;gBAAO;gBAC9E;oBAAE,SAAS;oBAAgB,SAAS;oBAAM,SAAS;oBAAM,QAAQ;gBAAU;gBAC3E;oBAAE,SAAS;oBAAiB,SAAS;oBAAM,SAAS;oBAAO,QAAQ;gBAAU;gBAC7E;oBAAE,SAAS;oBAAoB,SAAS;oBAAM,SAAS;oBAAM,QAAQ;gBAAU;aAChF;QACH;QACA;YACE,UAAU;YACV,OAAO;gBACL;oBAAE,SAAS;oBAAmB,SAAS;oBAAM,SAAS;oBAAQ,QAAQ;gBAAO;gBAC7E;oBAAE,SAAS;oBAAqB,SAAS;oBAAM,SAAS;oBAAQ,QAAQ;gBAAU;gBAClF;oBAAE,SAAS;oBAAqB,SAAS;oBAAM,SAAS;oBAAc,QAAQ;gBAAM;gBACpF;oBAAE,SAAS;oBAAoB,SAAS;oBAAM,SAAS;oBAAQ,QAAQ;gBAAO;gBAC9E;oBAAE,SAAS;oBAAoB,SAAS;oBAAM,SAAS;oBAAc,QAAQ;gBAAU;aACxF;QACH;KACD;IAED,MAAM,qBAAqB,CAAC;QAC1B,IAAI,UAAU,MAAM;YAClB,qBAAO,8OAAC,6MAAK;gBAAC,WAAU;;;;;;QAC1B,OAAO,IAAI,UAAU,OAAO;YAC1B,qBAAO,8OAAC,iMAAC;gBAAC,WAAU;;;;;;QACtB,OAAO;YACL,qBAAO,8OAAC;gBAAK,WAAU;0BAAuC;;;;;;QAChE;IACF;IAEA,MAAM,aAAa;QACjB;YACE,MAAM,uMAAG;YACT,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,gNAAM;YACZ,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,6MAAK;YACX,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,0MAAI;YACV,OAAO;YACP,aAAa;QACf;KACD;IAED,qBACE,8OAAC;QAAQ,IAAG;QAAW,WAAU;kBAC/B,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA0E;;;;;;sCAGxF,8OAAC;4BAAE,WAAU;sCAA+C;;;;;;;;;;;;8BAO9D,8OAAC;oBAAI,WAAU;8BACZ,WAAW,GAAG,CAAC,CAAC,WAAW;wBAC1B,MAAM,OAAO,UAAU,IAAI;wBAC3B,qBACE,8OAAC;4BAAgB,WAAU;;8CACzB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;;;;;;;;;;;8CAElB,8OAAC;oCAAG,WAAU;8CAA4C,UAAU,KAAK;;;;;;8CACzE,8OAAC;oCAAE,WAAU;8CAAiB,UAAU,WAAW;;;;;;;2BAL3C;;;;;oBAQd;;;;;;8BAIF,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA4C;;;;;;8CAG1D,8OAAC;oCAAE,WAAU;8CAAiC;;;;;;;;;;;;sCAKhD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAM,WAAU;;kDACf,8OAAC;wCAAM,WAAU;kDACf,cAAA,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAA0D;;;;;;8DAGxE,8OAAC;oDAAG,WAAU;8DAA4D;;;;;;8DAG1E,8OAAC;oDAAG,WAAU;8DAA4D;;;;;;8DAG1E,8OAAC;oDAAG,WAAU;8DAA4D;;;;;;;;;;;;;;;;;kDAK9E,8OAAC;wCAAM,WAAU;kDACd,SAAS,GAAG,CAAC,CAAC,UAAU,8BACvB,8OAAC,gNAAK,CAAC,QAAQ;;kEACb,8OAAC;wDAAqC,WAAU;kEAC9C,cAAA,8OAAC;4DAAG,SAAS;4DAAG,WAAU;sEACvB,SAAS,QAAQ;;;;;;uDAFb,CAAC,SAAS,EAAE,eAAe;;;;;oDAKnC,SAAS,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,0BACzB,8OAAC;4DAAyC,WAAU;;8EAClD,8OAAC;oEAAG,WAAU;8EACX,KAAK,OAAO;;;;;;8EAEf,8OAAC;oEAAG,WAAU;8EACX,mBAAmB,KAAK,OAAO;;;;;;8EAElC,8OAAC;oEAAG,WAAU;8EACX,mBAAmB,KAAK,OAAO;;;;;;8EAElC,8OAAC;oEAAG,WAAU;8EACX,mBAAmB,KAAK,MAAM;;;;;;;2DAX1B,GAAG,cAAc,CAAC,EAAE,WAAW;;;;;;+CAPvB,CAAC,eAAe,EAAE,eAAe;;;;;;;;;;;;;;;;;;;;;sCA4B9D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAG1C,8OAAC;oCAAO,WAAU;8CAAuJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrL", "debugId": null}}, {"offset": {"line": 680, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/webs/apiflow/packages/website/src/components/sections/ProductShowcase.tsx"], "sourcesContent": ["import { Play, Code, Database, Zap, Shield, Users, GitBranch, BarChart3 } from 'lucide-react';\n\nexport default function ProductShowcase() {\n  const features = [\n    {\n      icon: Code,\n      title: 'Intuitive API Builder',\n      description: 'Design and test APIs with our visual interface. No more complex configurations or steep learning curves.',\n      image: '/api/placeholder/400/300'\n    },\n    {\n      icon: Database,\n      title: 'Smart Mock Servers',\n      description: 'Generate realistic mock data automatically. Test your frontend before the backend is ready.',\n      image: '/api/placeholder/400/300'\n    },\n    {\n      icon: Zap,\n      title: 'Lightning Fast Testing',\n      description: 'Run comprehensive API tests in seconds. Automated testing workflows that scale with your team.',\n      image: '/api/placeholder/400/300'\n    },\n    {\n      icon: Shield,\n      title: 'Enterprise Security',\n      description: 'Built-in security testing and compliance features. Keep your APIs secure from day one.',\n      image: '/api/placeholder/400/300'\n    },\n    {\n      icon: Users,\n      title: 'Real-time Collaboration',\n      description: 'Work together seamlessly. Share collections, sync changes, and collaborate in real-time.',\n      image: '/api/placeholder/400/300'\n    },\n    {\n      icon: GitBranch,\n      title: 'Version Control Integration',\n      description: 'Native Git integration. Track changes, manage versions, and deploy with confidence.',\n      image: '/api/placeholder/400/300'\n    }\n  ];\n\n  const stats = [\n    { value: '50%', label: 'Faster API Development' },\n    { value: '99.9%', label: 'Uptime Guarantee' },\n    { value: '10K+', label: 'Happy Developers' },\n    { value: '24/7', label: 'Expert Support' }\n  ];\n\n  return (\n    <section id=\"demo\" className=\"py-16 sm:py-20 lg:py-24\">\n      <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        {/* Section header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl lg:text-5xl\">\n            See APIFlow in Action\n          </h2>\n          <p className=\"mx-auto mt-6 max-w-3xl text-lg text-gray-600\">\n            Experience the power of modern API development. From design to deployment, \n            APIFlow streamlines every step of your workflow.\n          </p>\n        </div>\n\n        {/* Main demo video/screenshot */}\n        <div className=\"mb-20\">\n          <div className=\"relative rounded-2xl bg-gradient-to-br from-blue-50 to-purple-50 shadow-2xl overflow-hidden\">\n            <div className=\"aspect-[16/9] flex items-center justify-center\">\n              <div className=\"text-center\">\n                <div className=\"w-20 h-20 bg-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg\">\n                  <Play className=\"w-10 h-10 text-white\" />\n                </div>\n                <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">\n                  Interactive Product Demo\n                </h3>\n                <p className=\"text-gray-600 mb-6 max-w-md mx-auto\">\n                  Watch how APIFlow transforms your API development workflow in just 3 minutes\n                </p>\n                <button className=\"inline-flex items-center rounded-lg bg-blue-600 px-6 py-3 text-base font-semibold text-white shadow-lg hover:bg-blue-700 transition-all duration-200 hover:scale-105\">\n                  <Play className=\"mr-2 h-5 w-5\" />\n                  Watch Demo\n                </button>\n              </div>\n            </div>\n            \n            {/* Floating UI elements for visual interest */}\n            <div className=\"absolute top-6 left-6 bg-white rounded-lg shadow-lg p-4 opacity-90 max-w-xs\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"w-3 h-3 bg-green-500 rounded-full\"></div>\n                <div>\n                  <div className=\"text-sm font-medium text-gray-900\">API Test Passed</div>\n                  <div className=\"text-xs text-gray-500\">Response time: 45ms</div>\n                </div>\n              </div>\n            </div>\n            \n            <div className=\"absolute top-6 right-6 bg-white rounded-lg shadow-lg p-4 opacity-90\">\n              <div className=\"flex items-center space-x-3\">\n                <BarChart3 className=\"w-5 h-5 text-blue-600\" />\n                <div>\n                  <div className=\"text-sm font-medium text-gray-900\">Performance</div>\n                  <div className=\"text-xs text-gray-500\">Excellent</div>\n                </div>\n              </div>\n            </div>\n            \n            <div className=\"absolute bottom-6 left-6 bg-white rounded-lg shadow-lg p-4 opacity-90\">\n              <div className=\"flex items-center space-x-3\">\n                <Users className=\"w-5 h-5 text-purple-600\" />\n                <div>\n                  <div className=\"text-sm font-medium text-gray-900\">Team Sync</div>\n                  <div className=\"text-xs text-gray-500\">3 members online</div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Stats */}\n        <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-8 mb-20\">\n          {stats.map((stat, index) => (\n            <div key={index} className=\"text-center\">\n              <div className=\"text-3xl font-bold text-blue-600 mb-2\">{stat.value}</div>\n              <div className=\"text-gray-600\">{stat.label}</div>\n            </div>\n          ))}\n        </div>\n\n        {/* Feature showcase */}\n        <div className=\"space-y-20\">\n          {features.map((feature, index) => {\n            const Icon = feature.icon;\n            const isEven = index % 2 === 0;\n            \n            return (\n              <div key={index} className={`flex flex-col lg:flex-row items-center gap-12 ${!isEven ? 'lg:flex-row-reverse' : ''}`}>\n                <div className=\"flex-1\">\n                  <div className=\"flex items-center mb-6\">\n                    <div className=\"w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mr-4\">\n                      <Icon className=\"w-6 h-6 text-blue-600\" />\n                    </div>\n                    <h3 className=\"text-2xl font-bold text-gray-900\">{feature.title}</h3>\n                  </div>\n                  <p className=\"text-lg text-gray-600 mb-6\">{feature.description}</p>\n                  <button className=\"inline-flex items-center text-blue-600 hover:text-blue-700 font-medium transition-colors duration-200\">\n                    Learn more\n                    <svg className=\"ml-2 w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                    </svg>\n                  </button>\n                </div>\n                <div className=\"flex-1\">\n                  <div className=\"relative rounded-xl bg-gradient-to-br from-gray-50 to-gray-100 shadow-lg overflow-hidden\">\n                    <div className=\"aspect-[4/3] flex items-center justify-center\">\n                      <div className=\"text-center\">\n                        <Icon className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n                        <p className=\"text-gray-500\">Feature Screenshot</p>\n                        <p className=\"text-sm text-gray-400 mt-2\">Coming Soon</p>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            );\n          })}\n        </div>\n\n        {/* CTA Section */}\n        <div className=\"mt-20 text-center\">\n          <div className=\"bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 lg:p-12\">\n            <h3 className=\"text-2xl lg:text-3xl font-bold text-white mb-4\">\n              Ready to Transform Your API Workflow?\n            </h3>\n            <p className=\"text-blue-100 mb-8 max-w-2xl mx-auto\">\n              Join thousands of developers who have already made the switch to APIFlow. \n              Start your free trial today and experience the difference.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <button className=\"inline-flex items-center rounded-lg bg-white px-8 py-3 text-base font-semibold text-blue-600 shadow-lg hover:bg-gray-50 transition-all duration-200\">\n                Start Free Trial\n              </button>\n              <button className=\"inline-flex items-center rounded-lg border-2 border-white px-8 py-3 text-base font-semibold text-white hover:bg-white hover:text-blue-600 transition-all duration-200\">\n                Schedule Demo\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAEe,SAAS;IACtB,MAAM,WAAW;QACf;YACE,MAAM,0MAAI;YACV,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM,sNAAQ;YACd,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM,uMAAG;YACT,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM,gNAAM;YACZ,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM,6MAAK;YACX,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM,6NAAS;YACf,OAAO;YACP,aAAa;YACb,OAAO;QACT;KACD;IAED,MAAM,QAAQ;QACZ;YAAE,OAAO;YAAO,OAAO;QAAyB;QAChD;YAAE,OAAO;YAAS,OAAO;QAAmB;QAC5C;YAAE,OAAO;YAAQ,OAAO;QAAmB;QAC3C;YAAE,OAAO;YAAQ,OAAO;QAAiB;KAC1C;IAED,qBACE,8OAAC;QAAQ,IAAG;QAAO,WAAU;kBAC3B,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA0E;;;;;;sCAGxF,8OAAC;4BAAE,WAAU;sCAA+C;;;;;;;;;;;;8BAO9D,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,0MAAI;gDAAC,WAAU;;;;;;;;;;;sDAElB,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;sDAGtD,8OAAC;4CAAE,WAAU;sDAAsC;;;;;;sDAGnD,8OAAC;4CAAO,WAAU;;8DAChB,8OAAC,0MAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;0CAOvC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;;8DACC,8OAAC;oDAAI,WAAU;8DAAoC;;;;;;8DACnD,8OAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;0CAK7C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,+NAAS;4CAAC,WAAU;;;;;;sDACrB,8OAAC;;8DACC,8OAAC;oDAAI,WAAU;8DAAoC;;;;;;8DACnD,8OAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;0CAK7C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,6MAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;;8DACC,8OAAC;oDAAI,WAAU;8DAAoC;;;;;;8DACnD,8OAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQjD,8OAAC;oBAAI,WAAU;8BACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;4BAAgB,WAAU;;8CACzB,8OAAC;oCAAI,WAAU;8CAAyC,KAAK,KAAK;;;;;;8CAClE,8OAAC;oCAAI,WAAU;8CAAiB,KAAK,KAAK;;;;;;;2BAFlC;;;;;;;;;;8BAQd,8OAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,SAAS;wBACtB,MAAM,OAAO,QAAQ,IAAI;wBACzB,MAAM,SAAS,QAAQ,MAAM;wBAE7B,qBACE,8OAAC;4BAAgB,WAAW,CAAC,8CAA8C,EAAE,CAAC,SAAS,wBAAwB,IAAI;;8CACjH,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;;;;;;;;;;;8DAElB,8OAAC;oDAAG,WAAU;8DAAoC,QAAQ,KAAK;;;;;;;;;;;;sDAEjE,8OAAC;4CAAE,WAAU;sDAA8B,QAAQ,WAAW;;;;;;sDAC9D,8OAAC;4CAAO,WAAU;;gDAAwG;8DAExH,8OAAC;oDAAI,WAAU;oDAAe,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACtE,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;8CAI3E,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;;;;;;kEAChB,8OAAC;wDAAE,WAAU;kEAAgB;;;;;;kEAC7B,8OAAC;wDAAE,WAAU;kEAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BAtB1C;;;;;oBA6Bd;;;;;;8BAIF,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAiD;;;;;;0CAG/D,8OAAC;gCAAE,WAAU;0CAAuC;;;;;;0CAIpD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAO,WAAU;kDAAsJ;;;;;;kDAGxK,8OAAC;wCAAO,WAAU;kDAAwK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxM", "debugId": null}}, {"offset": {"line": 1259, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/webs/apiflow/packages/website/src/components/sections/AboutSection.tsx"], "sourcesContent": ["import { Target, Users, Lightbulb, Award } from 'lucide-react';\n\nexport default function AboutSection() {\n  const values = [\n    {\n      icon: Target,\n      title: 'Developer-First',\n      description: 'Every feature is designed with developers in mind. We understand your workflow because we are developers too.'\n    },\n    {\n      icon: Users,\n      title: 'Community Driven',\n      description: 'Our roadmap is shaped by user feedback. We build what developers actually need, not what we think they need.'\n    },\n    {\n      icon: Lightbulb,\n      title: 'Innovation',\n      description: 'We constantly push the boundaries of what\\'s possible in API development and testing tools.'\n    },\n    {\n      icon: Award,\n      title: 'Excellence',\n      description: 'We strive for perfection in every detail, from user experience to performance and reliability.'\n    }\n  ];\n\n  const team = [\n    {\n      name: '<PERSON>',\n      role: 'CEO & Co-founder',\n      bio: 'Former lead engineer at major tech companies. Passionate about developer tools and API architecture.',\n      image: '/api/placeholder/150/150'\n    },\n    {\n      name: '<PERSON>',\n      role: '<PERSON><PERSON> & Co-founder',\n      bio: 'Full-stack developer with 10+ years experience. Expert in distributed systems and API design.',\n      image: '/api/placeholder/150/150'\n    },\n    {\n      name: '<PERSON>',\n      role: 'Head of Product',\n      bio: 'Product manager with deep understanding of developer workflows and enterprise needs.',\n      image: '/api/placeholder/150/150'\n    },\n    {\n      name: '<PERSON>',\n      role: 'Lead Designer',\n      bio: 'UX/UI designer focused on creating intuitive interfaces for complex technical tools.',\n      image: '/api/placeholder/150/150'\n    }\n  ];\n\n  const milestones = [\n    {\n      year: '2022',\n      title: 'Company Founded',\n      description: 'Started with a vision to simplify API development for everyone'\n    },\n    {\n      year: '2023',\n      title: 'First 1,000 Users',\n      description: 'Reached our first milestone with positive feedback from the community'\n    },\n    {\n      year: '2024',\n      title: 'Series A Funding',\n      description: 'Raised $10M to accelerate product development and team growth'\n    },\n    {\n      year: '2024',\n      title: '10,000+ Developers',\n      description: 'Growing community of developers using APIFlow daily'\n    }\n  ];\n\n  return (\n    <section id=\"about\" className=\"py-16 sm:py-20 lg:py-24\">\n      <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        {/* Section header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl lg:text-5xl\">\n            About APIFlow\n          </h2>\n          <p className=\"mx-auto mt-6 max-w-3xl text-lg text-gray-600\">\n            We're on a mission to make API development faster, easier, and more enjoyable \n            for developers around the world.\n          </p>\n        </div>\n\n        {/* Mission statement */}\n        <div className=\"bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-8 lg:p-12 mb-16\">\n          <div className=\"text-center\">\n            <h3 className=\"text-2xl lg:text-3xl font-bold text-gray-900 mb-6\">\n              Our Mission\n            </h3>\n            <p className=\"text-lg text-gray-700 max-w-4xl mx-auto leading-relaxed\">\n              To empower developers with the most intuitive, powerful, and collaborative API development \n              platform. We believe that great APIs are the foundation of great software, and we're here \n              to make building them a joy, not a chore.\n            </p>\n          </div>\n        </div>\n\n        {/* Values */}\n        <div className=\"mb-20\">\n          <h3 className=\"text-2xl font-bold text-gray-900 text-center mb-12\">\n            Our Values\n          </h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n            {values.map((value, index) => {\n              const Icon = value.icon;\n              return (\n                <div key={index} className=\"text-center\">\n                  <div className=\"mx-auto flex h-16 w-16 items-center justify-center rounded-xl bg-blue-100 mb-6\">\n                    <Icon className=\"h-8 w-8 text-blue-600\" />\n                  </div>\n                  <h4 className=\"text-lg font-semibold text-gray-900 mb-3\">{value.title}</h4>\n                  <p className=\"text-gray-600\">{value.description}</p>\n                </div>\n              );\n            })}\n          </div>\n        </div>\n\n        {/* Timeline */}\n        <div className=\"mb-20\">\n          <h3 className=\"text-2xl font-bold text-gray-900 text-center mb-12\">\n            Our Journey\n          </h3>\n          <div className=\"relative\">\n            {/* Timeline line */}\n            <div className=\"absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-blue-200\"></div>\n            \n            <div className=\"space-y-12\">\n              {milestones.map((milestone, index) => (\n                <div key={index} className={`flex items-center ${index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'}`}>\n                  <div className={`w-1/2 ${index % 2 === 0 ? 'pr-8 text-right' : 'pl-8 text-left'}`}>\n                    <div className=\"bg-white rounded-lg shadow-lg p-6\">\n                      <div className=\"text-blue-600 font-bold text-lg mb-2\">{milestone.year}</div>\n                      <h4 className=\"text-xl font-semibold text-gray-900 mb-3\">{milestone.title}</h4>\n                      <p className=\"text-gray-600\">{milestone.description}</p>\n                    </div>\n                  </div>\n                  <div className=\"relative flex items-center justify-center w-8 h-8\">\n                    <div className=\"w-4 h-4 bg-blue-600 rounded-full border-4 border-white shadow-lg\"></div>\n                  </div>\n                  <div className=\"w-1/2\"></div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n\n        {/* Team */}\n        <div>\n          <h3 className=\"text-2xl font-bold text-gray-900 text-center mb-12\">\n            Meet Our Team\n          </h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n            {team.map((member, index) => (\n              <div key={index} className=\"text-center\">\n                <div className=\"relative mb-6\">\n                  <div className=\"w-32 h-32 bg-gray-200 rounded-full mx-auto flex items-center justify-center\">\n                    <Users className=\"w-12 h-12 text-gray-400\" />\n                  </div>\n                </div>\n                <h4 className=\"text-lg font-semibold text-gray-900 mb-1\">{member.name}</h4>\n                <p className=\"text-blue-600 font-medium mb-3\">{member.role}</p>\n                <p className=\"text-gray-600 text-sm\">{member.bio}</p>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Join us CTA */}\n        <div className=\"mt-20 text-center\">\n          <div className=\"bg-gray-900 rounded-2xl p-8 lg:p-12\">\n            <h3 className=\"text-2xl lg:text-3xl font-bold text-white mb-4\">\n              Join Our Growing Team\n            </h3>\n            <p className=\"text-gray-300 mb-8 max-w-2xl mx-auto\">\n              We're always looking for talented individuals who share our passion for \n              developer tools and want to make a difference.\n            </p>\n            <button className=\"inline-flex items-center rounded-lg bg-blue-600 px-8 py-3 text-base font-semibold text-white shadow-lg hover:bg-blue-700 transition-all duration-200\">\n              View Open Positions\n            </button>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AAAA;AAAA;AAAA;;;AAEe,SAAS;IACtB,MAAM,SAAS;QACb;YACE,MAAM,gNAAM;YACZ,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,6MAAK;YACX,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,yNAAS;YACf,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,6MAAK;YACX,OAAO;YACP,aAAa;QACf;KACD;IAED,MAAM,OAAO;QACX;YACE,MAAM;YACN,MAAM;YACN,KAAK;YACL,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM;YACN,KAAK;YACL,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM;YACN,KAAK;YACL,OAAO;QACT;QACA;YACE,MAAM;YAC<PERSON>,MAAM;YACN,KAAK;YACL,OAAO;QACT;KACD;IAED,MAAM,aAAa;QACjB;YACE,MAAM;YACN,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;QACf;KACD;IAED,qBACE,8OAAC;QAAQ,IAAG;QAAQ,WAAU;kBAC5B,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA0E;;;;;;sCAGxF,8OAAC;4BAAE,WAAU;sCAA+C;;;;;;;;;;;;8BAO9D,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,8OAAC;gCAAE,WAAU;0CAA0D;;;;;;;;;;;;;;;;;8BAS3E,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAqD;;;;;;sCAGnE,8OAAC;4BAAI,WAAU;sCACZ,OAAO,GAAG,CAAC,CAAC,OAAO;gCAClB,MAAM,OAAO,MAAM,IAAI;gCACvB,qBACE,8OAAC;oCAAgB,WAAU;;sDACzB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;;;;;;;;;;;sDAElB,8OAAC;4CAAG,WAAU;sDAA4C,MAAM,KAAK;;;;;;sDACrE,8OAAC;4CAAE,WAAU;sDAAiB,MAAM,WAAW;;;;;;;mCALvC;;;;;4BAQd;;;;;;;;;;;;8BAKJ,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAqD;;;;;;sCAGnE,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;;;;;8CAEf,8OAAC;oCAAI,WAAU;8CACZ,WAAW,GAAG,CAAC,CAAC,WAAW,sBAC1B,8OAAC;4CAAgB,WAAW,CAAC,kBAAkB,EAAE,QAAQ,MAAM,IAAI,aAAa,oBAAoB;;8DAClG,8OAAC;oDAAI,WAAW,CAAC,MAAM,EAAE,QAAQ,MAAM,IAAI,oBAAoB,kBAAkB;8DAC/E,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAAwC,UAAU,IAAI;;;;;;0EACrE,8OAAC;gEAAG,WAAU;0EAA4C,UAAU,KAAK;;;;;;0EACzE,8OAAC;gEAAE,WAAU;0EAAiB,UAAU,WAAW;;;;;;;;;;;;;;;;;8DAGvD,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;;;;;;;;;;8DAEjB,8OAAC;oDAAI,WAAU;;;;;;;2CAXP;;;;;;;;;;;;;;;;;;;;;;8BAmBlB,8OAAC;;sCACC,8OAAC;4BAAG,WAAU;sCAAqD;;;;;;sCAGnE,8OAAC;4BAAI,WAAU;sCACZ,KAAK,GAAG,CAAC,CAAC,QAAQ,sBACjB,8OAAC;oCAAgB,WAAU;;sDACzB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,6MAAK;oDAAC,WAAU;;;;;;;;;;;;;;;;sDAGrB,8OAAC;4CAAG,WAAU;sDAA4C,OAAO,IAAI;;;;;;sDACrE,8OAAC;4CAAE,WAAU;sDAAkC,OAAO,IAAI;;;;;;sDAC1D,8OAAC;4CAAE,WAAU;sDAAyB,OAAO,GAAG;;;;;;;mCARxC;;;;;;;;;;;;;;;;8BAehB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAiD;;;;;;0CAG/D,8OAAC;gCAAE,WAAU;0CAAuC;;;;;;0CAIpD,8OAAC;gCAAO,WAAU;0CAAuJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrL", "debugId": null}}, {"offset": {"line": 1713, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/webs/apiflow/packages/website/src/components/sections/ContactSection.tsx"], "sourcesContent": ["import { Mail, MessageSquare, Phone, MapPin, Clock, Send } from 'lucide-react';\n\nexport default function ContactSection() {\n  const contactMethods = [\n    {\n      icon: Mail,\n      title: 'Email Support',\n      description: 'Get help from our support team',\n      contact: '<EMAIL>',\n      availability: '24/7 response within 4 hours'\n    },\n    {\n      icon: MessageSquare,\n      title: 'Live Chat',\n      description: 'Chat with us in real-time',\n      contact: 'Available in app',\n      availability: 'Mon-Fri, 9 AM - 6 PM PST'\n    },\n    {\n      icon: Phone,\n      title: 'Phone Support',\n      description: 'Speak directly with our team',\n      contact: '+****************',\n      availability: 'Enterprise customers only'\n    },\n    {\n      icon: MapPin,\n      title: 'Office',\n      description: 'Visit us in person',\n      contact: '123 Tech Street, San Francisco, CA',\n      availability: 'By appointment only'\n    }\n  ];\n\n  const faqs = [\n    {\n      question: 'How do I get started with APIFlow?',\n      answer: 'Simply sign up for a free account and you can start using APIFlow immediately. No credit card required for the free plan.'\n    },\n    {\n      question: 'Can I import my existing Postman collections?',\n      answer: 'Yes! We provide easy import tools to migrate your collections from Postman, Insomnia, and other popular API tools.'\n    },\n    {\n      question: 'Do you offer on-premise deployment?',\n      answer: 'Yes, we offer on-premise and private cloud deployment options for Enterprise customers with specific security requirements.'\n    },\n    {\n      question: 'What integrations do you support?',\n      answer: 'We integrate with popular tools like GitHub, GitLab, Slack, Jira, and many more. Check our integrations page for the full list.'\n    },\n    {\n      question: 'Is my data secure?',\n      answer: 'Absolutely. We use enterprise-grade security measures including encryption at rest and in transit, SOC 2 compliance, and regular security audits.'\n    },\n    {\n      question: 'Can I cancel my subscription anytime?',\n      answer: 'Yes, you can cancel your subscription at any time. There are no long-term contracts or cancellation fees.'\n    }\n  ];\n\n  return (\n    <section id=\"contact\" className=\"py-16 sm:py-20 lg:py-24 bg-gray-50\">\n      <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        {/* Section header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl lg:text-5xl\">\n            Get in Touch\n          </h2>\n          <p className=\"mx-auto mt-6 max-w-3xl text-lg text-gray-600\">\n            Have questions? Need help getting started? Our team is here to support you \n            every step of the way.\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-16\">\n          {/* Contact form */}\n          <div className=\"bg-white rounded-2xl shadow-lg p-8\">\n            <h3 className=\"text-2xl font-bold text-gray-900 mb-6\">\n              Send us a message\n            </h3>\n            <form className=\"space-y-6\">\n              <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-6\">\n                <div>\n                  <label htmlFor=\"firstName\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    First Name\n                  </label>\n                  <input\n                    type=\"text\"\n                    id=\"firstName\"\n                    name=\"firstName\"\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-600 focus:border-transparent transition-colors duration-200\"\n                    placeholder=\"John\"\n                  />\n                </div>\n                <div>\n                  <label htmlFor=\"lastName\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Last Name\n                  </label>\n                  <input\n                    type=\"text\"\n                    id=\"lastName\"\n                    name=\"lastName\"\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-600 focus:border-transparent transition-colors duration-200\"\n                    placeholder=\"Doe\"\n                  />\n                </div>\n              </div>\n              \n              <div>\n                <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Email Address\n                </label>\n                <input\n                  type=\"email\"\n                  id=\"email\"\n                  name=\"email\"\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-600 focus:border-transparent transition-colors duration-200\"\n                  placeholder=\"<EMAIL>\"\n                />\n              </div>\n              \n              <div>\n                <label htmlFor=\"company\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Company (Optional)\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"company\"\n                  name=\"company\"\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-600 focus:border-transparent transition-colors duration-200\"\n                  placeholder=\"Your Company\"\n                />\n              </div>\n              \n              <div>\n                <label htmlFor=\"subject\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Subject\n                </label>\n                <select\n                  id=\"subject\"\n                  name=\"subject\"\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-600 focus:border-transparent transition-colors duration-200\"\n                >\n                  <option value=\"\">Select a topic</option>\n                  <option value=\"general\">General Inquiry</option>\n                  <option value=\"support\">Technical Support</option>\n                  <option value=\"sales\">Sales Question</option>\n                  <option value=\"partnership\">Partnership</option>\n                  <option value=\"feedback\">Feedback</option>\n                </select>\n              </div>\n              \n              <div>\n                <label htmlFor=\"message\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Message\n                </label>\n                <textarea\n                  id=\"message\"\n                  name=\"message\"\n                  rows={6}\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-600 focus:border-transparent transition-colors duration-200\"\n                  placeholder=\"Tell us how we can help you...\"\n                ></textarea>\n              </div>\n              \n              <button\n                type=\"submit\"\n                className=\"w-full inline-flex items-center justify-center rounded-lg bg-blue-600 px-6 py-3 text-base font-semibold text-white shadow-lg hover:bg-blue-700 transition-all duration-200\"\n              >\n                <Send className=\"mr-2 h-5 w-5\" />\n                Send Message\n              </button>\n            </form>\n          </div>\n\n          {/* Contact methods and FAQ */}\n          <div className=\"space-y-8\">\n            {/* Contact methods */}\n            <div className=\"bg-white rounded-2xl shadow-lg p-8\">\n              <h3 className=\"text-2xl font-bold text-gray-900 mb-6\">\n                Other ways to reach us\n              </h3>\n              <div className=\"space-y-6\">\n                {contactMethods.map((method, index) => {\n                  const Icon = method.icon;\n                  return (\n                    <div key={index} className=\"flex items-start\">\n                      <div className=\"flex-shrink-0\">\n                        <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center\">\n                          <Icon className=\"w-6 h-6 text-blue-600\" />\n                        </div>\n                      </div>\n                      <div className=\"ml-4\">\n                        <h4 className=\"text-lg font-semibold text-gray-900\">{method.title}</h4>\n                        <p className=\"text-gray-600 mb-1\">{method.description}</p>\n                        <p className=\"text-blue-600 font-medium\">{method.contact}</p>\n                        <p className=\"text-sm text-gray-500\">{method.availability}</p>\n                      </div>\n                    </div>\n                  );\n                })}\n              </div>\n            </div>\n\n            {/* Quick start CTA */}\n            <div className=\"bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 text-center\">\n              <h3 className=\"text-2xl font-bold text-white mb-4\">\n                Ready to Get Started?\n              </h3>\n              <p className=\"text-blue-100 mb-6\">\n                Don't wait! Start building better APIs today with our free plan.\n              </p>\n              <button className=\"inline-flex items-center rounded-lg bg-white px-6 py-3 text-base font-semibold text-blue-600 shadow-lg hover:bg-gray-50 transition-all duration-200\">\n                Start Free Trial\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* FAQ Section */}\n        <div className=\"mt-20\">\n          <h3 className=\"text-2xl font-bold text-gray-900 text-center mb-12\">\n            Frequently Asked Questions\n          </h3>\n          <div className=\"bg-white rounded-2xl shadow-lg p-8\">\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n              {faqs.map((faq, index) => (\n                <div key={index} className=\"border-b border-gray-200 pb-6 last:border-b-0\">\n                  <h4 className=\"text-lg font-semibold text-gray-900 mb-3\">\n                    {faq.question}\n                  </h4>\n                  <p className=\"text-gray-600\">{faq.answer}</p>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n\n        {/* Business hours */}\n        <div className=\"mt-12 text-center\">\n          <div className=\"inline-flex items-center rounded-full bg-blue-100 px-6 py-3 text-blue-800\">\n            <Clock className=\"mr-2 h-5 w-5\" />\n            <span className=\"font-medium\">Support Hours: Monday - Friday, 9 AM - 6 PM PST</span>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAEe,SAAS;IACtB,MAAM,iBAAiB;QACrB;YACE,MAAM,0MAAI;YACV,OAAO;YACP,aAAa;YACb,SAAS;YACT,cAAc;QAChB;QACA;YACE,MAAM,yOAAa;YACnB,OAAO;YACP,aAAa;YACb,SAAS;YACT,cAAc;QAChB;QACA;YACE,MAAM,6MAAK;YACX,OAAO;YACP,aAAa;YACb,SAAS;YACT,cAAc;QAChB;QACA;YACE,MAAM,oNAAM;YACZ,OAAO;YACP,aAAa;YACb,SAAS;YACT,cAAc;QAChB;KACD;IAED,MAAM,OAAO;QACX;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;KACD;IAED,qBACE,8OAAC;QAAQ,IAAG;QAAU,WAAU;kBAC9B,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA0E;;;;;;sCAGxF,8OAAC;4BAAE,WAAU;sCAA+C;;;;;;;;;;;;8BAM9D,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,8OAAC;oCAAK,WAAU;;sDACd,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAM,SAAQ;4DAAY,WAAU;sEAA+C;;;;;;sEAGpF,8OAAC;4DACC,MAAK;4DACL,IAAG;4DACH,MAAK;4DACL,WAAU;4DACV,aAAY;;;;;;;;;;;;8DAGhB,8OAAC;;sEACC,8OAAC;4DAAM,SAAQ;4DAAW,WAAU;sEAA+C;;;;;;sEAGnF,8OAAC;4DACC,MAAK;4DACL,IAAG;4DACH,MAAK;4DACL,WAAU;4DACV,aAAY;;;;;;;;;;;;;;;;;;sDAKlB,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAQ,WAAU;8DAA+C;;;;;;8DAGhF,8OAAC;oDACC,MAAK;oDACL,IAAG;oDACH,MAAK;oDACL,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAU,WAAU;8DAA+C;;;;;;8DAGlF,8OAAC;oDACC,MAAK;oDACL,IAAG;oDACH,MAAK;oDACL,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAU,WAAU;8DAA+C;;;;;;8DAGlF,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACL,WAAU;;sEAEV,8OAAC;4DAAO,OAAM;sEAAG;;;;;;sEACjB,8OAAC;4DAAO,OAAM;sEAAU;;;;;;sEACxB,8OAAC;4DAAO,OAAM;sEAAU;;;;;;sEACxB,8OAAC;4DAAO,OAAM;sEAAQ;;;;;;sEACtB,8OAAC;4DAAO,OAAM;sEAAc;;;;;;sEAC5B,8OAAC;4DAAO,OAAM;sEAAW;;;;;;;;;;;;;;;;;;sDAI7B,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAU,WAAU;8DAA+C;;;;;;8DAGlF,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACL,MAAM;oDACN,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,8OAAC;4CACC,MAAK;4CACL,WAAU;;8DAEV,8OAAC,0MAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;sCAOvC,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;sDAGtD,8OAAC;4CAAI,WAAU;sDACZ,eAAe,GAAG,CAAC,CAAC,QAAQ;gDAC3B,MAAM,OAAO,OAAO,IAAI;gDACxB,qBACE,8OAAC;oDAAgB,WAAU;;sEACzB,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAK,WAAU;;;;;;;;;;;;;;;;sEAGpB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EAAuC,OAAO,KAAK;;;;;;8EACjE,8OAAC;oEAAE,WAAU;8EAAsB,OAAO,WAAW;;;;;;8EACrD,8OAAC;oEAAE,WAAU;8EAA6B,OAAO,OAAO;;;;;;8EACxD,8OAAC;oEAAE,WAAU;8EAAyB,OAAO,YAAY;;;;;;;;;;;;;mDAVnD;;;;;4CAcd;;;;;;;;;;;;8CAKJ,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAqC;;;;;;sDAGnD,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAGlC,8OAAC;4CAAO,WAAU;sDAAsJ;;;;;;;;;;;;;;;;;;;;;;;;8BAQ9K,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAqD;;;;;;sCAGnE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ,KAAK,GAAG,CAAC,CAAC,KAAK,sBACd,8OAAC;wCAAgB,WAAU;;0DACzB,8OAAC;gDAAG,WAAU;0DACX,IAAI,QAAQ;;;;;;0DAEf,8OAAC;gDAAE,WAAU;0DAAiB,IAAI,MAAM;;;;;;;uCAJhC;;;;;;;;;;;;;;;;;;;;;8BAYlB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,6MAAK;gCAAC,WAAU;;;;;;0CACjB,8OAAC;gCAAK,WAAU;0CAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM1C", "debugId": null}}, {"offset": {"line": 2335, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/webs/apiflow/packages/website/src/app/%5Blocale%5D/page.tsx"], "sourcesContent": ["import HeroSection from \"@/components/sections/HeroSection\";\nimport ComparisonSection from \"@/components/sections/ComparisonSection\";\nimport ProductShowcase from \"@/components/sections/ProductShowcase\";\nimport AboutSection from \"@/components/sections/AboutSection\";\nimport ContactSection from \"@/components/sections/ContactSection\";\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen\">\n      <HeroSection />\n      <ComparisonSection />\n      <ProductShowcase />\n      <AboutSection />\n      <ContactSection />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;;;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,+KAAW;;;;;0BACZ,8OAAC,qLAAiB;;;;;0BAClB,8OAAC,mLAAe;;;;;0BAChB,8OAAC,gLAAY;;;;;0BACb,8OAAC,kLAAc;;;;;;;;;;;AAGrB", "debugId": null}}]}