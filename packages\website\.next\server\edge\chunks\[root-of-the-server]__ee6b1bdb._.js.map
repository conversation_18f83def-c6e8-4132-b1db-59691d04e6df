{"version": 3, "sources": [], "sections": [{"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/packages/website/src/middleware.ts"], "sourcesContent": ["import createMiddleware from 'next-intl/middleware';\n\nexport default createMiddleware({\n  // A list of all locales that are supported\n  locales: ['zh', 'en'],\n\n  // Used when no locale matches\n  defaultLocale: 'zh',\n\n  // Always use locale prefix\n  localePrefix: 'as-needed'\n});\n\nexport const config = {\n  // Match only internationalized pathnames\n  matcher: ['/((?!api|_next|_vercel|.*\\\\..*).*)']\n};\n"], "names": [], "mappings": ";;;;;;AAAA;;uCAEe,IAAA,yMAAgB,EAAC;IAC9B,2CAA2C;IAC3C,SAAS;QAAC;QAAM;KAAK;IAErB,8BAA8B;IAC9B,eAAe;IAEf,2BAA2B;IAC3B,cAAc;AAChB;AAEO,MAAM,SAAS;IACpB,yCAAyC;IACzC,SAAS;QAAC;KAAqC;AACjD"}}]}