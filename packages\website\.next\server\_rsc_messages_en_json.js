"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_messages_en_json";
exports.ids = ["_rsc_messages_en_json"];
exports.modules = {

/***/ "(rsc)/./messages/en.json":
/*!**************************!*\
  !*** ./messages/en.json ***!
  \**************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"navigation":{"features":"Features","demo":"Demo","about":"About","contact":"Contact","tryDemo":"Try Demo","getStarted":"Get Started","github":"GitHub","gitee":"Gitee"},"hero":{"badge":"🚀 New: Advanced API Testing Features Available","title":"The Modern Alternative to","titleHighlight":"Postman & Apifox","subtitle":"Build, test, and document your APIs with our intuitive platform. Experience faster workflows, better collaboration, and comprehensive API management in one powerful tool.","startFreeTrial":"Start Free Trial","watchDemo":"Watch Demo","trustedBy":"Trusted by developers at leading companies","stats":{"developers":"Developers","rating":"Rating","uptime":"Uptime"},"demoPlaceholder":"Interactive Demo Coming Soon","demoDescription":"See APIFlow in action with real API testing scenarios","apiConnected":"API Connected","testsPassing":"Tests Passing"},"comparison":{"title":"Why Choose APIFlow?","subtitle":"See how APIFlow compares to other popular API tools. We\'ve built everything you love about existing tools, plus the features you\'ve been waiting for.","highlights":{"faster":{"title":"3x Faster","description":"API testing workflows compared to traditional tools"},"enterpriseReady":{"title":"Enterprise Ready","description":"Built-in security and compliance features from day one"},"teamFocused":{"title":"Team Focused","description":"Real-time collaboration without the premium price tag"},"developerFirst":{"title":"Developer First","description":"Designed by developers, for developers"}},"tableTitle":"Feature Comparison","tableSubtitle":"See how we stack up against the competition","features":{"coreFeatures":"Core Features","apiTesting":"API Testing & Documentation","realTimeCollaboration":"Real-time Collaboration","advancedMockServers":"Advanced Mock Servers","automatedTesting":"Automated Testing Workflows","graphqlSupport":"GraphQL Support","developerExperience":"Developer Experience","intuitiveInterface":"Intuitive Interface","fastPerformance":"Fast Performance","offlineMode":"Offline Mode","customThemes":"Custom Themes","pluginEcosystem":"Plugin Ecosystem","enterprise":"Enterprise","ssoIntegration":"SSO Integration","advancedSecurity":"Advanced Security","customDeployment":"Custom Deployment","prioritySupport":"Priority Support","complianceReady":"Compliance Ready"},"values":{"limited":"limited","paid":"paid","good":"good","slow":"slow","enterprise":"enterprise"},"readyToExperience":"Ready to experience the difference?","startTrial":"Start Your Free Trial"},"showcase":{"title":"See APIFlow in Action","subtitle":"Experience the power of modern API development. From design to deployment, APIFlow streamlines every step of your workflow.","demoTitle":"Interactive Product Demo","demoDescription":"Watch how APIFlow transforms your API development workflow in just 3 minutes","watchDemo":"Watch Demo","stats":{"fasterDevelopment":"Faster API Development","uptimeGuarantee":"Uptime Guarantee","happyDevelopers":"Happy Developers","expertSupport":"Expert Support"},"features":{"apiBuilder":{"title":"Intuitive API Builder","description":"Design and test APIs with our visual interface. No more complex configurations or steep learning curves."},"mockServers":{"title":"Smart Mock Servers","description":"Generate realistic mock data automatically. Test your frontend before the backend is ready."},"fastTesting":{"title":"Lightning Fast Testing","description":"Run comprehensive API tests in seconds. Automated testing workflows that scale with your team."},"enterpriseSecurity":{"title":"Enterprise Security","description":"Built-in security testing and compliance features. Keep your APIs secure from day one."},"realTimeCollaboration":{"title":"Real-time Collaboration","description":"Work together seamlessly. Share collections, sync changes, and collaborate in real-time."},"versionControl":{"title":"Version Control Integration","description":"Native Git integration. Track changes, manage versions, and deploy with confidence."}},"learnMore":"Learn more","featureScreenshot":"Feature Screenshot","comingSoon":"Coming Soon","ctaTitle":"Ready to Transform Your API Workflow?","ctaDescription":"Join thousands of developers who have already made the switch to APIFlow. Start your free trial today and experience the difference.","startFreeTrial":"Start Free Trial","scheduleDemo":"Schedule Demo"},"about":{"title":"About APIFlow","subtitle":"We\'re on a mission to make API development faster, easier, and more enjoyable for developers around the world.","missionTitle":"Our Mission","missionDescription":"To empower developers with the most intuitive, powerful, and collaborative API development platform. We believe that great APIs are the foundation of great software, and we\'re here to make building them a joy, not a chore.","valuesTitle":"Our Values","values":{"developerFirst":{"title":"Developer-First","description":"Every feature is designed with developers in mind. We understand your workflow because we are developers too."},"communityDriven":{"title":"Community Driven","description":"Our roadmap is shaped by user feedback. We build what developers actually need, not what we think they need."},"innovation":{"title":"Innovation","description":"We constantly push the boundaries of what\'s possible in API development and testing tools."},"excellence":{"title":"Excellence","description":"We strive for perfection in every detail, from user experience to performance and reliability."}},"journeyTitle":"Our Journey","milestones":{"founded":{"title":"Company Founded","description":"Started with a vision to simplify API development for everyone"},"firstUsers":{"title":"First 1,000 Users","description":"Reached our first milestone with positive feedback from the community"},"seriesA":{"title":"Series A Funding","description":"Raised $10M to accelerate product development and team growth"},"tenThousandDevelopers":{"title":"10,000+ Developers","description":"Growing community of developers using APIFlow daily"}},"teamTitle":"Meet Our Team","team":{"alexJohnson":{"name":"Alex Johnson","role":"CEO & Co-founder","bio":"Former lead engineer at major tech companies. Passionate about developer tools and API architecture."},"sarahChen":{"name":"Sarah Chen","role":"CTO & Co-founder","bio":"Full-stack developer with 10+ years experience. Expert in distributed systems and API design."},"michaelRodriguez":{"name":"Michael Rodriguez","role":"Head of Product","bio":"Product manager with deep understanding of developer workflows and enterprise needs."},"emilyDavis":{"name":"Emily Davis","role":"Lead Designer","bio":"UX/UI designer focused on creating intuitive interfaces for complex technical tools."}},"joinTeamTitle":"Join Our Growing Team","joinTeamDescription":"We\'re always looking for talented individuals who share our passion for developer tools and want to make a difference.","viewPositions":"View Open Positions"},"contact":{"title":"Get in Touch","subtitle":"Have questions? Need help getting started? Our team is here to support you every step of the way.","sendMessage":"Send us a message","form":{"firstName":"First Name","lastName":"Last Name","email":"Email Address","company":"Company (Optional)","subject":"Subject","message":"Message","selectTopic":"Select a topic","topics":{"general":"General Inquiry","support":"Technical Support","sales":"Sales Question","partnership":"Partnership","feedback":"Feedback"},"messagePlaceholder":"Tell us how we can help you...","sendMessage":"Send Message"},"otherWays":"Other ways to reach us","contactMethods":{"emailSupport":{"title":"Email Support","description":"Get help from our support team","contact":"<EMAIL>","availability":"24/7 response within 4 hours"},"liveChat":{"title":"Live Chat","description":"Chat with us in real-time","contact":"Available in app","availability":"Mon-Fri, 9 AM - 6 PM PST"},"phoneSupport":{"title":"Phone Support","description":"Speak directly with our team","contact":"+****************","availability":"Enterprise customers only"},"office":{"title":"Office","description":"Visit us in person","contact":"123 Tech Street, San Francisco, CA","availability":"By appointment only"}},"readyToStart":"Ready to Get Started?","readyDescription":"Don\'t wait! Start building better APIs today with our free plan.","startFreeTrial":"Start Free Trial","faqTitle":"Frequently Asked Questions","faqs":{"howToStart":{"question":"How do I get started with APIFlow?","answer":"Simply sign up for a free account and you can start using APIFlow immediately. No credit card required for the free plan."},"importPostman":{"question":"Can I import my existing Postman collections?","answer":"Yes! We provide easy import tools to migrate your collections from Postman, Insomnia, and other popular API tools."},"onPremise":{"question":"Do you offer on-premise deployment?","answer":"Yes, we offer on-premise and private cloud deployment options for Enterprise customers with specific security requirements."},"integrations":{"question":"What integrations do you support?","answer":"We integrate with popular tools like GitHub, GitLab, Slack, Jira, and many more. Check our integrations page for the full list."},"dataSecurity":{"question":"Is my data secure?","answer":"Absolutely. We use enterprise-grade security measures including encryption at rest and in transit, SOC 2 compliance, and regular security audits."},"cancelSubscription":{"question":"Can I cancel my subscription anytime?","answer":"Yes, you can cancel your subscription at any time. There are no long-term contracts or cancellation fees."}},"supportHours":"Support Hours: Monday - Friday, 9 AM - 6 PM PST"},"footer":{"description":"The modern API documentation and testing tool that developers love. Build, test, and document your APIs with ease.","product":"Product","company":"Company","resources":"Resources","legal":"Legal","links":{"features":"Features","demo":"Demo","documentation":"Documentation","about":"About","blog":"Blog","careers":"Careers","contact":"Contact","helpCenter":"Help Center","apiReference":"API Reference","community":"Community","status":"Status","privacy":"Privacy Policy","terms":"Terms of Service","cookies":"Cookie Policy","security":"Security"},"copyright":"© 2024 APIFlow. All rights reserved.","builtWith":"Built with ❤️ for developers worldwide"},"openSource":{"badge":"Open Source","description":"APIFlow is an open source project, welcoming community contributions","starOnGitHub":"Star on GitHub","viewOnGitee":"View on Gitee","contribute":"Contribute","license":"MIT License"}}');

/***/ })

};
;