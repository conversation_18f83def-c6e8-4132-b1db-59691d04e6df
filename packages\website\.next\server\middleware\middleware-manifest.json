{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_64d8cd1e._.js", "server/edge/chunks/[root-of-the-server]__ee6b1bdb._.js", "server/edge/chunks/turbopack-packages_website_edge-wrapper_d0b1f80c.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next|_vercel|.*\\..*).*){(\\\\.json)}?", "originalSource": "/((?!api|_next|_vercel|.*\\..*).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "afg8pKhnCsgeS22ryFG1dd5x9bVj9fneOlAn7UPDJog=", "__NEXT_PREVIEW_MODE_ID": "662d12315add2904ad4f2a2b1220e5cf", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "0498c6160cab449dcb466ae70b23c2bec8f26bb9efd204d89b785d2ca1ae2e97", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "751ea28d93719299ff03ba6241f5c9e865c30935b444c05b892d3b41d463a860"}}}, "instrumentation": null, "functions": {}}