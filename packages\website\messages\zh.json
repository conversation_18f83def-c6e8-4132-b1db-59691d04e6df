{"navigation": {"features": "功能特性", "demo": "产品演示", "about": "关于我们", "contact": "联系我们", "github": "GitHub"}, "hero": {"badge": "🚀 新功能：高级 API 测试功能现已上线", "title": "Postman 和 Apifox 的", "titleHighlight": "现代化替代品", "subtitle": "使用我们直观的平台构建、测试和记录您的 API。体验更快的工作流程、更好的协作和全面的 API 管理，一个强大的工具搞定一切。", "downloadOffline": "下载离线版本", "downloadOnline": "下载互联网版本", "stats": {"developers": "开发者", "rating": "评分", "uptime": "正常运行时间"}}, "comparison": {"title": "为什么选择 APIFlow？", "subtitle": "了解 APIFlow 与其他流行 API 工具的对比。我们构建了您喜爱的现有工具的所有功能，以及您一直在等待的新功能。", "highlights": {"faster": {"title": "快 3 倍", "description": "相比传统工具，API 测试工作流程快 3 倍"}, "enterpriseReady": {"title": "企业级就绪", "description": "从第一天起就内置安全性和合规性功能"}, "teamFocused": {"title": "团队协作", "description": "无需高级价格标签的实时协作"}, "developerFirst": {"title": "开发者优先", "description": "由开发者设计，为开发者服务"}}, "tableTitle": "功能对比", "tableSubtitle": "看看我们与竞争对手的对比", "features": {"coreFeatures": "核心功能", "apiTesting": "API 测试与文档", "realTimeCollaboration": "实时协作", "advancedMockServers": "高级模拟服务器", "automatedTesting": "自动化测试工作流", "graphqlSupport": "GraphQL 支持", "developerExperience": "开发者体验", "intuitiveInterface": "直观界面", "fastPerformance": "快速性能", "offlineMode": "离线模式", "customThemes": "自定义主题", "pluginEcosystem": "插件生态系统", "enterprise": "企业功能", "ssoIntegration": "SSO 集成", "advancedSecurity": "高级安全", "customDeployment": "自定义部署", "prioritySupport": "优先支持", "complianceReady": "合规就绪"}, "values": {"limited": "有限", "paid": "付费", "good": "良好", "slow": "较慢", "enterprise": "企业版"}, "readyToExperience": "准备体验不同之处？", "startTrial": "开始免费试用"}, "footer": {"description": "开发者喜爱的现代化 API 文档与测试工具。轻松构建、测试和记录您的 API。", "product": "产品", "company": "公司", "resources": "资源", "legal": "法律", "links": {"features": "功能特性", "demo": "演示", "documentation": "文档", "about": "关于我们", "blog": "博客", "careers": "招聘", "contact": "联系我们", "helpCenter": "帮助中心", "apiReference": "API 参考", "community": "社区", "status": "状态", "privacy": "隐私政策", "terms": "服务条款", "cookies": "<PERSON><PERSON> 政策", "security": "安全"}, "copyright": "© 2024 APIFlow. 保留所有权利。", "builtWith": "为全世界的开发者用 ❤️ 构建"}, "openSource": {"badge": "开源项目", "description": "APIFlow 是一个开源项目，欢迎社区贡献", "starOnGitHub": "在 GitHub 上加星", "viewOnGitee": "在 Gitee 上查看", "contribute": "贡献代码", "license": "MIT 许可证"}}