{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/webs/apiflow/packages/website/src/i18n.ts"], "sourcesContent": ["import {notFound} from 'next/navigation';\nimport {getRequestConfig} from 'next-intl/server';\n\n// Can be imported from a shared config\nexport const locales = ['zh', 'en'] as const;\n\nexport default getRequestConfig(async ({requestLocale}) => {\n  // This typically corresponds to the `[locale]` segment\n  let locale = await requestLocale;\n\n  // Ensure that a valid locale is used\n  if (!locale || !locales.includes(locale as any)) {\n    locale = 'zh';\n  }\n\n  return {\n    locale,\n    messages: (await import(`../messages/${locale}.json`)).default\n  };\n});\n"], "names": [], "mappings": ";;;;;;AACA;;AAGO,MAAM,UAAU;IAAC;IAAM;CAAK;uCAEpB,IAAA,8QAAgB,EAAC,OAAO,EAAC,aAAa,EAAC;IACpD,uDAAuD;IACvD,IAAI,SAAS,MAAM;IAEnB,qCAAqC;IACrC,IAAI,CAAC,UAAU,CAAC,QAAQ,QAAQ,CAAC,SAAgB;QAC/C,SAAS;IACX;IAEA,OAAO;QACL;QACA,UAAU,CAAC;;;;;;;;;kBAAa,CAAC,YAAY,EAAE,OAAO,KAAK,CAAC,CAAC,EAAE,OAAO;IAChE;AACF", "debugId": null}}, {"offset": {"line": 41, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/webs/apiflow/packages/website/src/components/layout/Header.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/packages/website/src/components/layout/Header.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/website/src/components/layout/Header.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAqT,GAClV,mFACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/webs/apiflow/packages/website/src/components/layout/Header.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/packages/website/src/components/layout/Header.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/website/src/components/layout/Header.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAiS,GAC9T,+DACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 69, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 77, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/webs/apiflow/packages/website/src/components/layout/Footer.tsx"], "sourcesContent": ["import Link from 'next/link';\nimport { GitBranch, MessageCircle, Users, Mail } from 'lucide-react';\nimport { useTranslations } from 'next-intl';\n\nexport default function Footer() {\n  const t = useTranslations('页脚');\n  const footerLinks = {\n    product: [\n      { name: t('链接.功能特性'), href: '#features' },\n      { name: t('链接.演示'), href: '#demo' },\n      { name: t('链接.文档'), href: '#docs' },\n    ],\n    company: [\n      { name: t('链接.关于我们'), href: '#about' },\n      { name: t('链接.博客'), href: '#blog' },\n      { name: t('链接.招聘'), href: '#careers' },\n      { name: t('链接.联系我们'), href: '#contact' },\n    ],\n    resources: [\n      { name: t('链接.帮助中心'), href: '#help' },\n      { name: t('链接.API参考'), href: '#api' },\n      { name: t('链接.社区'), href: '#community' },\n      { name: t('链接.状态'), href: '#status' },\n    ],\n    legal: [\n      { name: t('链接.隐私政策'), href: '#privacy' },\n      { name: t('链接.服务条款'), href: '#terms' },\n      { name: t('链接.Cookie政策'), href: '#cookies' },\n      { name: t('链接.安全'), href: '#security' },\n    ],\n  };\n\n  const socialLinks = [\n    { name: 'GitHub', icon: GitBranch, href: '#github' },\n    { name: 'Twitter', icon: MessageCircle, href: '#twitter' },\n    { name: 'LinkedIn', icon: Users, href: '#linkedin' },\n    { name: 'Email', icon: Mail, href: 'mailto:<EMAIL>' },\n  ];\n\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8\">\n          {/* Brand Section */}\n          <div className=\"lg:col-span-2\">\n            <div className=\"flex items-center space-x-2 mb-4\">\n              <div className=\"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-lg\">A</span>\n              </div>\n              <span className=\"text-xl font-bold\">APIFlow</span>\n            </div>\n            <p className=\"text-gray-400 mb-6 max-w-md\">\n              {t('描述')}\n            </p>\n            <div className=\"flex space-x-4\">\n              {socialLinks.map((social) => {\n                const Icon = social.icon;\n                return (\n                  <Link\n                    key={social.name}\n                    href={social.href}\n                    className=\"text-gray-400 hover:text-white transition-colors duration-200\"\n                    aria-label={social.name}\n                  >\n                    <Icon size={20} />\n                  </Link>\n                );\n              })}\n            </div>\n          </div>\n\n          {/* Product Links */}\n          <div>\n            <h3 className=\"text-sm font-semibold text-white uppercase tracking-wider mb-4\">\n              {t('产品')}\n            </h3>\n            <ul className=\"space-y-3\">\n              {footerLinks.product.map((link) => (\n                <li key={link.name}>\n                  <Link\n                    href={link.href}\n                    className=\"text-gray-400 hover:text-white transition-colors duration-200\"\n                  >\n                    {link.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Company Links */}\n          <div>\n            <h3 className=\"text-sm font-semibold text-white uppercase tracking-wider mb-4\">\n              {t('公司')}\n            </h3>\n            <ul className=\"space-y-3\">\n              {footerLinks.company.map((link) => (\n                <li key={link.name}>\n                  <Link\n                    href={link.href}\n                    className=\"text-gray-400 hover:text-white transition-colors duration-200\"\n                  >\n                    {link.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Resources Links */}\n          <div>\n            <h3 className=\"text-sm font-semibold text-white uppercase tracking-wider mb-4\">\n              {t('资源')}\n            </h3>\n            <ul className=\"space-y-3\">\n              {footerLinks.resources.map((link) => (\n                <li key={link.name}>\n                  <Link\n                    href={link.href}\n                    className=\"text-gray-400 hover:text-white transition-colors duration-200\"\n                  >\n                    {link.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Legal Links */}\n          <div>\n            <h3 className=\"text-sm font-semibold text-white uppercase tracking-wider mb-4\">\n              {t('法律')}\n            </h3>\n            <ul className=\"space-y-3\">\n              {footerLinks.legal.map((link) => (\n                <li key={link.name}>\n                  <Link\n                    href={link.href}\n                    className=\"text-gray-400 hover:text-white transition-colors duration-200\"\n                  >\n                    {link.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n        </div>\n\n        {/* Bottom Section */}\n        <div className=\"mt-12 pt-8 border-t border-gray-800\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center\">\n            <p className=\"text-gray-400 text-sm\">\n              {t('版权')}\n            </p>\n            <p className=\"text-gray-400 text-sm mt-4 md:mt-0\">\n              {t('构建者')}\n            </p>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;AAAA;AAAA;AACA;;;;;AAEe,SAAS;IACtB,MAAM,IAAI,IAAA,iQAAe,EAAC;IAC1B,MAAM,cAAc;QAClB,SAAS;YACP;gBAAE,MAAM,EAAE;gBAAY,MAAM;YAAY;YACxC;gBAAE,MAAM,EAAE;gBAAU,MAAM;YAAQ;YAClC;gBAAE,MAAM,EAAE;gBAAU,MAAM;YAAQ;SACnC;QACD,SAAS;YACP;gBAAE,MAAM,EAAE;gBAAY,MAAM;YAAS;YACrC;gBAAE,MAAM,EAAE;gBAAU,MAAM;YAAQ;YAClC;gBAAE,MAAM,EAAE;gBAAU,MAAM;YAAW;YACrC;gBAAE,MAAM,EAAE;gBAAY,MAAM;YAAW;SACxC;QACD,WAAW;YACT;gBAAE,MAAM,EAAE;gBAAY,MAAM;YAAQ;YACpC;gBAAE,MAAM,EAAE;gBAAa,MAAM;YAAO;YACpC;gBAAE,MAAM,EAAE;gBAAU,MAAM;YAAa;YACvC;gBAAE,MAAM,EAAE;gBAAU,MAAM;YAAU;SACrC;QACD,OAAO;YACL;gBAAE,MAAM,EAAE;gBAAY,MAAM;YAAW;YACvC;gBAAE,MAAM,EAAE;gBAAY,MAAM;YAAS;YACrC;gBAAE,MAAM,EAAE;gBAAgB,MAAM;YAAW;YAC3C;gBAAE,MAAM,EAAE;gBAAU,MAAM;YAAY;SACvC;IACH;IAEA,MAAM,cAAc;QAClB;YAAE,MAAM;YAAU,MAAM,6NAAS;YAAE,MAAM;QAAU;QACnD;YAAE,MAAM;YAAW,MAAM,yOAAa;YAAE,MAAM;QAAW;QACzD;YAAE,MAAM;YAAY,MAAM,6MAAK;YAAE,MAAM;QAAY;QACnD;YAAE,MAAM;YAAS,MAAM,0MAAI;YAAE,MAAM;QAA6B;KACjE;IAED,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,8OAAC;4CAAK,WAAU;sDAAoB;;;;;;;;;;;;8CAEtC,8OAAC;oCAAE,WAAU;8CACV,EAAE;;;;;;8CAEL,8OAAC;oCAAI,WAAU;8CACZ,YAAY,GAAG,CAAC,CAAC;wCAChB,MAAM,OAAO,OAAO,IAAI;wCACxB,qBACE,8OAAC,uKAAI;4CAEH,MAAM,OAAO,IAAI;4CACjB,WAAU;4CACV,cAAY,OAAO,IAAI;sDAEvB,cAAA,8OAAC;gDAAK,MAAM;;;;;;2CALP,OAAO,IAAI;;;;;oCAQtB;;;;;;;;;;;;sCAKJ,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CACX,EAAE;;;;;;8CAEL,8OAAC;oCAAG,WAAU;8CACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC;sDACC,cAAA,8OAAC,uKAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CACX,EAAE;;;;;;8CAEL,8OAAC;oCAAG,WAAU;8CACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC;sDACC,cAAA,8OAAC,uKAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CACX,EAAE;;;;;;8CAEL,8OAAC;oCAAG,WAAU;8CACX,YAAY,SAAS,CAAC,GAAG,CAAC,CAAC,qBAC1B,8OAAC;sDACC,cAAA,8OAAC,uKAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CACX,EAAE;;;;;;8CAEL,8OAAC;oCAAG,WAAU;8CACX,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,qBACtB,8OAAC;sDACC,cAAA,8OAAC,uKAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;8BAc1B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CACV,EAAE;;;;;;0CAEL,8OAAC;gCAAE,WAAU;0CACV,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjB", "debugId": null}}, {"offset": {"line": 475, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/webs/apiflow/packages/website/src/components/seo/StructuredData.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/packages/website/src/components/seo/StructuredData.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/website/src/components/seo/StructuredData.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAA0T,GACvV,wFACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 489, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/webs/apiflow/packages/website/src/components/seo/StructuredData.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/packages/website/src/components/seo/StructuredData.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/website/src/components/seo/StructuredData.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAsS,GACnU,oEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 503, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 511, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/webs/apiflow/packages/website/src/app/%5Blocale%5D/layout.tsx"], "sourcesContent": ["import {NextIntlClientProvider} from 'next-intl';\nimport {getMessages} from 'next-intl/server';\nimport Header from \"@/components/layout/Header\";\nimport Footer from \"@/components/layout/Footer\";\nimport StructuredData from \"@/components/seo/StructuredData\";\n\nexport default async function LocaleLayout({\n  children,\n  params\n}: {\n  children: React.ReactNode;\n  params: Promise<{locale: string}>;\n}) {\n  const {locale} = await params;\n  // Providing all messages to the client\n  // side is the easiest way to get started\n  const messages = await getMessages();\n\n  return (\n    <html lang={locale}>\n      <body>\n        <NextIntlClientProvider messages={messages}>\n          <StructuredData />\n          <Header />\n          <main className=\"min-h-screen\">\n            {children}\n          </main>\n          <Footer />\n        </NextIntlClientProvider>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;;;;;;;AAEe,eAAe,aAAa,EACzC,QAAQ,EACR,MAAM,EAIP;IACC,MAAM,EAAC,MAAM,EAAC,GAAG,MAAM;IACvB,uCAAuC;IACvC,yCAAyC;IACzC,MAAM,WAAW,MAAM,IAAA,+PAAW;IAElC,qBACE,8OAAC;QAAK,MAAM;kBACV,cAAA,8OAAC;sBACC,cAAA,8OAAC,4RAAsB;gBAAC,UAAU;;kCAChC,8OAAC,6KAAc;;;;;kCACf,8OAAC,wKAAM;;;;;kCACP,8OAAC;wBAAK,WAAU;kCACb;;;;;;kCAEH,8OAAC,wKAAM;;;;;;;;;;;;;;;;;;;;;AAKjB", "debugId": null}}]}