{"导航": {"功能特性": "Features", "产品演示": "Demo", "关于我们": "About", "联系我们": "Contact", "GitHub": "GitHub"}, "首页": {"徽章": "🚀 New: Advanced API Testing Features Now Available", "标题": "Modern Alternative to", "标题高亮": "Postman & Apifox", "副标题": "Build, test, and document your APIs with our intuitive platform. Experience faster workflows, better collaboration, and comprehensive API management in one powerful tool.", "下载离线版本": "Download Offline Version", "下载互联网版本": "Download Online Version", "统计": {"开发者": "Developers", "评分": "Rating", "正常运行时间": "Uptime"}}, "对比": {"标题": "Why Choose APIFlow?", "副标题": "See how APIFlow compares to other popular API tools. We've built all the features you love from existing tools, plus the new ones you've been waiting for.", "亮点": {"更快": {"标题": "3x Faster", "描述": "API testing workflows are 3x faster compared to traditional tools"}, "企业级就绪": {"标题": "Enterprise Ready", "描述": "Built-in security and compliance features from day one"}, "团队协作": {"标题": "Team Collaboration", "描述": "Real-time collaboration without premium price tags"}, "开发者优先": {"标题": "Developer First", "描述": "Built by developers, for developers"}}, "表格标题": "Feature Comparison", "表格副标题": "See how we stack up against the competition", "功能": {"核心功能": "Core Features", "API测试与文档": "API Testing & Documentation", "实时协作": "Real-time Collaboration", "高级模拟服务器": "Advanced Mock Servers", "自动化测试工作流": "Automated Testing Workflows", "GraphQL支持": "GraphQL Support", "开发者体验": "Developer Experience", "直观界面": "Intuitive Interface", "快速性能": "Fast Performance", "离线模式": "Offline Mode", "自定义主题": "Custom Themes", "插件生态系统": "Plugin Ecosystem", "企业功能": "Enterprise Features", "SSO集成": "SSO Integration", "高级安全": "Advanced Security", "自定义部署": "Custom Deployment", "优先支持": "Priority Support", "合规就绪": "Compliance Ready"}, "值": {"有限": "Limited", "付费": "Paid", "良好": "Good", "较慢": "Slow", "企业版": "Enterprise"}, "准备体验": "Ready to experience the difference?", "开始试用": "Start Free Trial"}, "页脚": {"描述": "Modern API documentation and testing tool loved by developers. Build, test, and document your APIs with ease.", "产品": "Product", "公司": "Company", "资源": "Resources", "法律": "Legal", "链接": {"功能特性": "Features", "演示": "Demo", "文档": "Documentation", "关于我们": "About", "博客": "Blog", "招聘": "Careers", "联系我们": "Contact", "帮助中心": "Help Center", "API参考": "API Reference", "社区": "Community", "状态": "Status", "隐私政策": "Privacy Policy", "服务条款": "Terms of Service", "Cookie政策": "<PERSON><PERSON>", "安全": "Security"}, "版权": "© 2024 APIFlow. All rights reserved.", "构建者": "Built with ❤️ for developers worldwide"}, "开源": {"徽章": "Open Source", "描述": "APIFlow is an open source project, community contributions welcome", "GitHub加星": "Star on GitHub", "Gitee查看": "View on Gitee", "贡献": "Contribute", "许可证": "MIT License"}}