{"navigation": {"features": "Features", "demo": "Demo", "about": "About", "contact": "Contact", "github": "GitHub"}, "hero": {"badge": "🚀 New: Advanced API Testing Features Now Available", "title": "Modern Alternative to", "titleHighlight": "Postman & Apifox", "subtitle": "Build, test, and document your APIs with our intuitive platform. Experience faster workflows, better collaboration, and comprehensive API management in one powerful tool.", "downloadOffline": "Download Offline Version", "downloadOnline": "Download Online Version", "stats": {"developers": "Developers", "rating": "Rating", "uptime": "Uptime"}}, "comparison": {"title": "Why Choose APIFlow?", "subtitle": "See how APIFlow compares to other popular API tools. We've built all the features you love from existing tools, plus the new ones you've been waiting for.", "highlights": {"faster": {"title": "3x Faster", "description": "API testing workflows are 3x faster compared to traditional tools"}, "enterpriseReady": {"title": "Enterprise Ready", "description": "Built-in security and compliance features from day one"}, "teamFocused": {"title": "Team Collaboration", "description": "Real-time collaboration without premium price tags"}, "developerFirst": {"title": "Developer First", "description": "Built by developers, for developers"}}, "tableTitle": "Feature Comparison", "tableSubtitle": "See how we stack up against the competition", "features": {"coreFeatures": "Core Features", "apiTesting": "API Testing & Documentation", "realTimeCollaboration": "Real-time Collaboration", "advancedMockServers": "Advanced Mock Servers", "automatedTesting": "Automated Testing Workflows", "graphqlSupport": "GraphQL Support", "developerExperience": "Developer Experience", "intuitiveInterface": "Intuitive Interface", "fastPerformance": "Fast Performance", "offlineMode": "Offline Mode", "customThemes": "Custom Themes", "pluginEcosystem": "Plugin Ecosystem", "enterprise": "Enterprise Features", "ssoIntegration": "SSO Integration", "advancedSecurity": "Advanced Security", "customDeployment": "Custom Deployment", "prioritySupport": "Priority Support", "complianceReady": "Compliance Ready"}, "values": {"limited": "Limited", "paid": "Paid", "good": "Good", "slow": "Slow", "enterprise": "Enterprise"}, "readyToExperience": "Ready to experience the difference?", "startTrial": "Start Free Trial"}, "footer": {"description": "Modern API documentation and testing tool loved by developers. Build, test, and document your APIs with ease.", "product": "Product", "company": "Company", "resources": "Resources", "legal": "Legal", "links": {"features": "Features", "demo": "Demo", "documentation": "Documentation", "about": "About", "blog": "Blog", "careers": "Careers", "contact": "Contact", "helpCenter": "Help Center", "apiReference": "API Reference", "community": "Community", "status": "Status", "privacy": "Privacy Policy", "terms": "Terms of Service", "cookies": "<PERSON><PERSON>", "security": "Security"}, "copyright": "© 2024 APIFlow. All rights reserved.", "builtWith": "Built with ❤️ for developers worldwide"}, "openSource": {"badge": "Open Source", "description": "APIFlow is an open source project, community contributions welcome", "starOnGitHub": "Star on GitHub", "viewOnGitee": "View on Gitee", "contribute": "Contribute", "license": "MIT License"}}