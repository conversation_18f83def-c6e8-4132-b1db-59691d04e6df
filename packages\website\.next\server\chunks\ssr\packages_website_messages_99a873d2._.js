module.exports = [
"[project]/packages/website/messages/en.json (json, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/packages_website_messages_en_json_67eed851._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/packages/website/messages/en.json (json)");
    });
});
}),
"[project]/packages/website/messages/zh.json (json, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/packages_website_messages_zh_json_e5ceaf93._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/packages/website/messages/zh.json (json)");
    });
});
}),
];