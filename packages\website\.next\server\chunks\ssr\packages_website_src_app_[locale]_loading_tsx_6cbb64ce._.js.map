{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/webs/apiflow/packages/website/src/app/%5Blocale%5D/loading.tsx"], "sourcesContent": ["export default function Loading() {\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n      <div className=\"text-center\">\n        <div className=\"w-16 h-16 bg-blue-600 rounded-xl flex items-center justify-center mx-auto mb-4 animate-pulse\">\n          <span className=\"text-white font-bold text-2xl\">A</span>\n        </div>\n        <div className=\"text-gray-600 font-medium\">Loading APIFlow...</div>\n        <div className=\"mt-4\">\n          <div className=\"w-32 h-1 bg-gray-200 rounded-full mx-auto overflow-hidden\">\n            <div className=\"w-full h-full bg-blue-600 rounded-full animate-pulse\"></div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAK,WAAU;kCAAgC;;;;;;;;;;;8BAElD,8OAAC;oBAAI,WAAU;8BAA4B;;;;;;8BAC3C,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3B", "debugId": null}}]}