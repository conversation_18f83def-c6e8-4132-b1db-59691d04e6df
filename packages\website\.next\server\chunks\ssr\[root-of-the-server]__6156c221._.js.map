{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/webs/apiflow/packages/website/src/components/sections/HeroSection.tsx"], "sourcesContent": ["import Link from 'next/link';\nimport { Star, Users, Zap } from 'lucide-react';\nimport { useTranslations } from 'next-intl';\n\nexport default function HeroSection() {\n  const t = useTranslations('hero');\n  const stats = [\n    { icon: Users, value: '10K+', label: t('stats.developers') },\n    { icon: Star, value: '4.9/5', label: t('stats.rating') },\n    { icon: Zap, value: '99.9%', label: t('stats.uptime') },\n  ];\n\n  return (\n    <section className=\"relative pt-20 pb-16 sm:pt-24 sm:pb-20 lg:pt-32 lg:pb-28\">\n      {/* Background gradient */}\n      <div className=\"absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-purple-50\"></div>\n\n      <div className=\"relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n          {/* Left side - Content */}\n          <div className=\"text-left\">\n            {/* Badge */}\n            <div className=\"inline-flex items-center rounded-full bg-blue-100 px-4 py-2 text-sm font-medium text-blue-800 mb-8\">\n              {t('badge')}\n            </div>\n\n            {/* Main heading */}\n            <h1 className=\"text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl lg:text-6xl\">\n              {t('title')}{' '}\n              <span className=\"gradient-text\">{t('titleHighlight')}</span>\n            </h1>\n\n            {/* Subheading */}\n            <p className=\"mt-6 max-w-2xl text-lg leading-8 text-gray-600 sm:text-xl\">\n              {t('subtitle')}\n            </p>\n\n            {/* CTA Buttons */}\n            <div className=\"mt-10 flex flex-col sm:flex-row items-start gap-4\">\n              <Link\n                href=\"https://github.com/trueleaf/apiflow/releases\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"inline-flex items-center rounded-lg bg-blue-600 px-8 py-3 text-base font-semibold text-white shadow-lg hover:bg-blue-700\"\n              >\n                {t('downloadOffline')}\n              </Link>\n              <Link\n                href=\"https://github.com/trueleaf/apiflow\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"inline-flex items-center rounded-lg bg-green-600 px-8 py-3 text-base font-semibold text-white shadow-lg hover:bg-green-700\"\n              >\n                {t('downloadOnline')}\n              </Link>\n            </div>\n          </div>\n\n          {/* Right side - Stats */}\n          <div className=\"lg:pl-8\">\n            <div className=\"grid grid-cols-1 gap-8 sm:grid-cols-3 lg:grid-cols-1 lg:gap-6\">\n              {stats.map((stat, index) => {\n                const Icon = stat.icon;\n                return (\n                  <div key={index} className=\"flex items-center lg:justify-start justify-center\">\n                    <div className=\"flex h-12 w-12 items-center justify-center rounded-lg bg-blue-100 mr-4\">\n                      <Icon className=\"h-6 w-6 text-blue-600\" />\n                    </div>\n                    <div>\n                      <div className=\"text-2xl font-bold text-gray-900\">{stat.value}</div>\n                      <div className=\"text-sm text-gray-600\">{stat.label}</div>\n                    </div>\n                  </div>\n                );\n              })}\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;AAAA;AACA;;;;;AAEe,SAAS;IACtB,MAAM,IAAI,IAAA,iQAAe,EAAC;IAC1B,MAAM,QAAQ;QACZ;YAAE,MAAM,6MAAK;YAAE,OAAO;YAAQ,OAAO,EAAE;QAAoB;QAC3D;YAAE,MAAM,0MAAI;YAAE,OAAO;YAAS,OAAO,EAAE;QAAgB;QACvD;YAAE,MAAM,uMAAG;YAAE,OAAO;YAAS,OAAO,EAAE;QAAgB;KACvD;IAED,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;;;;;0BAEf,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACZ,EAAE;;;;;;8CAIL,8OAAC;oCAAG,WAAU;;wCACX,EAAE;wCAAU;sDACb,8OAAC;4CAAK,WAAU;sDAAiB,EAAE;;;;;;;;;;;;8CAIrC,8OAAC;oCAAE,WAAU;8CACV,EAAE;;;;;;8CAIL,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,uKAAI;4CACH,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,WAAU;sDAET,EAAE;;;;;;sDAEL,8OAAC,uKAAI;4CACH,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,WAAU;sDAET,EAAE;;;;;;;;;;;;;;;;;;sCAMT,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ,MAAM,GAAG,CAAC,CAAC,MAAM;oCAChB,MAAM,OAAO,KAAK,IAAI;oCACtB,qBACE,8OAAC;wCAAgB,WAAU;;0DACzB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;;;;;;;;;;;0DAElB,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;kEAAoC,KAAK,KAAK;;;;;;kEAC7D,8OAAC;wDAAI,WAAU;kEAAyB,KAAK,KAAK;;;;;;;;;;;;;uCAN5C;;;;;gCAUd;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOd", "debugId": null}}, {"offset": {"line": 232, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/webs/apiflow/packages/website/src/components/sections/ComparisonSection.tsx"], "sourcesContent": ["import React from 'react';\nimport { Check, X, Zap, Shield, Users, Code } from 'lucide-react';\nimport { useTranslations } from 'next-intl';\n\nexport default function ComparisonSection() {\n  const t = useTranslations('comparison');\n  const features = [\n    {\n      category: 'Core Features',\n      items: [\n        { feature: 'API Testing & Documentation', apiflow: true, postman: true, apifox: true },\n        { feature: 'Real-time Collaboration', apiflow: true, postman: 'limited', apifox: true },\n        { feature: 'Advanced Mock Servers', apiflow: true, postman: 'paid', apifox: true },\n        { feature: 'Automated Testing Workflows', apiflow: true, postman: 'paid', apifox: 'limited' },\n        { feature: 'GraphQL Support', apiflow: true, postman: true, apifox: true },\n      ]\n    },\n    {\n      category: 'Developer Experience',\n      items: [\n        { feature: 'Intuitive Interface', apiflow: true, postman: 'good', apifox: 'good' },\n        { feature: 'Fast Performance', apiflow: true, postman: 'slow', apifox: 'good' },\n        { feature: 'Offline Mode', apiflow: true, postman: true, apifox: 'limited' },\n        { feature: 'Custom Themes', apiflow: true, postman: false, apifox: 'limited' },\n        { feature: 'Plugin Ecosystem', apiflow: true, postman: true, apifox: 'limited' },\n      ]\n    },\n    {\n      category: 'Enterprise',\n      items: [\n        { feature: 'SSO Integration', apiflow: true, postman: 'paid', apifox: 'paid' },\n        { feature: 'Advanced Security', apiflow: true, postman: 'paid', apifox: 'limited' },\n        { feature: 'Custom Deployment', apiflow: true, postman: 'enterprise', apifox: false },\n        { feature: 'Priority Support', apiflow: true, postman: 'paid', apifox: 'paid' },\n        { feature: 'Compliance Ready', apiflow: true, postman: 'enterprise', apifox: 'limited' },\n      ]\n    }\n  ];\n\n  const renderFeatureValue = (value: boolean | string) => {\n    if (value === true) {\n      return <Check className=\"h-5 w-5 text-green-600\" />;\n    } else if (value === false) {\n      return <X className=\"h-5 w-5 text-red-500\" />;\n    } else {\n      return <span className=\"text-xs text-yellow-600 font-medium\">{value}</span>;\n    }\n  };\n\n  const highlights = [\n    {\n      icon: Zap,\n      title: '3x Faster',\n      description: 'API testing workflows compared to traditional tools'\n    },\n    {\n      icon: Shield,\n      title: 'Enterprise Ready',\n      description: 'Built-in security and compliance features from day one'\n    },\n    {\n      icon: Users,\n      title: 'Team Focused',\n      description: 'Real-time collaboration without the premium price tag'\n    },\n    {\n      icon: Code,\n      title: 'Developer First',\n      description: 'Designed by developers, for developers'\n    }\n  ];\n\n  return (\n    <section id=\"features\" className=\"py-16 sm:py-20 lg:py-24 bg-gray-50\">\n      <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        {/* Section header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl lg:text-5xl\">\n            Why Choose APIFlow?\n          </h2>\n          <p className=\"mx-auto mt-6 max-w-3xl text-lg text-gray-600\">\n            See how APIFlow compares to other popular API tools. We've built everything \n            you love about existing tools, plus the features you've been waiting for.\n          </p>\n        </div>\n\n        {/* Highlights */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16\">\n          {highlights.map((highlight, index) => {\n            const Icon = highlight.icon;\n            return (\n              <div key={index} className=\"text-center\">\n                <div className=\"mx-auto flex h-16 w-16 items-center justify-center rounded-xl bg-blue-600 mb-4\">\n                  <Icon className=\"h-8 w-8 text-white\" />\n                </div>\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">{highlight.title}</h3>\n                <p className=\"text-gray-600\">{highlight.description}</p>\n              </div>\n            );\n          })}\n        </div>\n\n        {/* Comparison table */}\n        <div className=\"bg-white rounded-2xl shadow-xl overflow-hidden\">\n          <div className=\"px-6 py-8 bg-gradient-to-r from-blue-600 to-purple-600\">\n            <h3 className=\"text-2xl font-bold text-white text-center\">\n              Feature Comparison\n            </h3>\n            <p className=\"text-blue-100 text-center mt-2\">\n              See how we stack up against the competition\n            </p>\n          </div>\n\n          <div className=\"overflow-x-auto\">\n            <table className=\"w-full\">\n              <thead className=\"bg-gray-50\">\n                <tr>\n                  <th className=\"px-6 py-4 text-left text-sm font-semibold text-gray-900\">\n                    Feature\n                  </th>\n                  <th className=\"px-6 py-4 text-center text-sm font-semibold text-blue-600\">\n                    APIFlow\n                  </th>\n                  <th className=\"px-6 py-4 text-center text-sm font-semibold text-gray-600\">\n                    Postman\n                  </th>\n                  <th className=\"px-6 py-4 text-center text-sm font-semibold text-gray-600\">\n                    Apifox\n                  </th>\n                </tr>\n              </thead>\n              <tbody className=\"divide-y divide-gray-200\">\n                {features.map((category, categoryIndex) => (\n                  <React.Fragment key={`category-group-${categoryIndex}`}>\n                    <tr key={`category-${categoryIndex}`} className=\"bg-gray-50\">\n                      <td colSpan={4} className=\"px-6 py-3 text-sm font-semibold text-gray-700\">\n                        {category.category}\n                      </td>\n                    </tr>\n                    {category.items.map((item, itemIndex) => (\n                      <tr key={`${categoryIndex}-${itemIndex}`} className=\"hover:bg-gray-50\">\n                        <td className=\"px-6 py-4 text-sm text-gray-900\">\n                          {item.feature}\n                        </td>\n                        <td className=\"px-6 py-4 text-center\">\n                          {renderFeatureValue(item.apiflow)}\n                        </td>\n                        <td className=\"px-6 py-4 text-center\">\n                          {renderFeatureValue(item.postman)}\n                        </td>\n                        <td className=\"px-6 py-4 text-center\">\n                          {renderFeatureValue(item.apifox)}\n                        </td>\n                      </tr>\n                    ))}\n                  </React.Fragment>\n                ))}\n              </tbody>\n            </table>\n          </div>\n\n          <div className=\"px-6 py-6 bg-blue-50 text-center\">\n            <p className=\"text-sm text-gray-600 mb-4\">\n              Ready to experience the difference?\n            </p>\n            <button className=\"inline-flex items-center rounded-lg bg-blue-600 px-6 py-3 text-base font-semibold text-white shadow-lg hover:bg-blue-700 transition-all duration-200\">\n              Start Your Free Trial\n            </button>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;;;AAEe,SAAS;IACtB,MAAM,IAAI,IAAA,iQAAe,EAAC;IAC1B,MAAM,WAAW;QACf;YACE,UAAU;YACV,OAAO;gBACL;oBAAE,SAAS;oBAA+B,SAAS;oBAAM,SAAS;oBAAM,QAAQ;gBAAK;gBACrF;oBAAE,SAAS;oBAA2B,SAAS;oBAAM,SAAS;oBAAW,QAAQ;gBAAK;gBACtF;oBAAE,SAAS;oBAAyB,SAAS;oBAAM,SAAS;oBAAQ,QAAQ;gBAAK;gBACjF;oBAAE,SAAS;oBAA+B,SAAS;oBAAM,SAAS;oBAAQ,QAAQ;gBAAU;gBAC5F;oBAAE,SAAS;oBAAmB,SAAS;oBAAM,SAAS;oBAAM,QAAQ;gBAAK;aAC1E;QACH;QACA;YACE,UAAU;YACV,OAAO;gBACL;oBAAE,SAAS;oBAAuB,SAAS;oBAAM,SAAS;oBAAQ,QAAQ;gBAAO;gBACjF;oBAAE,SAAS;oBAAoB,SAAS;oBAAM,SAAS;oBAAQ,QAAQ;gBAAO;gBAC9E;oBAAE,SAAS;oBAAgB,SAAS;oBAAM,SAAS;oBAAM,QAAQ;gBAAU;gBAC3E;oBAAE,SAAS;oBAAiB,SAAS;oBAAM,SAAS;oBAAO,QAAQ;gBAAU;gBAC7E;oBAAE,SAAS;oBAAoB,SAAS;oBAAM,SAAS;oBAAM,QAAQ;gBAAU;aAChF;QACH;QACA;YACE,UAAU;YACV,OAAO;gBACL;oBAAE,SAAS;oBAAmB,SAAS;oBAAM,SAAS;oBAAQ,QAAQ;gBAAO;gBAC7E;oBAAE,SAAS;oBAAqB,SAAS;oBAAM,SAAS;oBAAQ,QAAQ;gBAAU;gBAClF;oBAAE,SAAS;oBAAqB,SAAS;oBAAM,SAAS;oBAAc,QAAQ;gBAAM;gBACpF;oBAAE,SAAS;oBAAoB,SAAS;oBAAM,SAAS;oBAAQ,QAAQ;gBAAO;gBAC9E;oBAAE,SAAS;oBAAoB,SAAS;oBAAM,SAAS;oBAAc,QAAQ;gBAAU;aACxF;QACH;KACD;IAED,MAAM,qBAAqB,CAAC;QAC1B,IAAI,UAAU,MAAM;YAClB,qBAAO,8OAAC,6MAAK;gBAAC,WAAU;;;;;;QAC1B,OAAO,IAAI,UAAU,OAAO;YAC1B,qBAAO,8OAAC,iMAAC;gBAAC,WAAU;;;;;;QACtB,OAAO;YACL,qBAAO,8OAAC;gBAAK,WAAU;0BAAuC;;;;;;QAChE;IACF;IAEA,MAAM,aAAa;QACjB;YACE,MAAM,uMAAG;YACT,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,gNAAM;YACZ,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,6MAAK;YACX,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,0MAAI;YACV,OAAO;YACP,aAAa;QACf;KACD;IAED,qBACE,8OAAC;QAAQ,IAAG;QAAW,WAAU;kBAC/B,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA0E;;;;;;sCAGxF,8OAAC;4BAAE,WAAU;sCAA+C;;;;;;;;;;;;8BAO9D,8OAAC;oBAAI,WAAU;8BACZ,WAAW,GAAG,CAAC,CAAC,WAAW;wBAC1B,MAAM,OAAO,UAAU,IAAI;wBAC3B,qBACE,8OAAC;4BAAgB,WAAU;;8CACzB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;;;;;;;;;;;8CAElB,8OAAC;oCAAG,WAAU;8CAA4C,UAAU,KAAK;;;;;;8CACzE,8OAAC;oCAAE,WAAU;8CAAiB,UAAU,WAAW;;;;;;;2BAL3C;;;;;oBAQd;;;;;;8BAIF,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA4C;;;;;;8CAG1D,8OAAC;oCAAE,WAAU;8CAAiC;;;;;;;;;;;;sCAKhD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAM,WAAU;;kDACf,8OAAC;wCAAM,WAAU;kDACf,cAAA,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAA0D;;;;;;8DAGxE,8OAAC;oDAAG,WAAU;8DAA4D;;;;;;8DAG1E,8OAAC;oDAAG,WAAU;8DAA4D;;;;;;8DAG1E,8OAAC;oDAAG,WAAU;8DAA4D;;;;;;;;;;;;;;;;;kDAK9E,8OAAC;wCAAM,WAAU;kDACd,SAAS,GAAG,CAAC,CAAC,UAAU,8BACvB,8OAAC,gNAAK,CAAC,QAAQ;;kEACb,8OAAC;wDAAqC,WAAU;kEAC9C,cAAA,8OAAC;4DAAG,SAAS;4DAAG,WAAU;sEACvB,SAAS,QAAQ;;;;;;uDAFb,CAAC,SAAS,EAAE,eAAe;;;;;oDAKnC,SAAS,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,0BACzB,8OAAC;4DAAyC,WAAU;;8EAClD,8OAAC;oEAAG,WAAU;8EACX,KAAK,OAAO;;;;;;8EAEf,8OAAC;oEAAG,WAAU;8EACX,mBAAmB,KAAK,OAAO;;;;;;8EAElC,8OAAC;oEAAG,WAAU;8EACX,mBAAmB,KAAK,OAAO;;;;;;8EAElC,8OAAC;oEAAG,WAAU;8EACX,mBAAmB,KAAK,MAAM;;;;;;;2DAX1B,GAAG,cAAc,CAAC,EAAE,WAAW;;;;;;+CAPvB,CAAC,eAAe,EAAE,eAAe;;;;;;;;;;;;;;;;;;;;;sCA4B9D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAG1C,8OAAC;oCAAO,WAAU;8CAAuJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrL", "debugId": null}}, {"offset": {"line": 698, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/webs/apiflow/packages/website/src/app/%5Blocale%5D/page.tsx"], "sourcesContent": ["import HeroSection from \"@/components/sections/HeroSection\";\nimport ComparisonSection from \"@/components/sections/ComparisonSection\";\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen\">\n      <HeroSection />\n      <ComparisonSection />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,+KAAW;;;;;0BACZ,8OAAC,qLAAiB;;;;;;;;;;;AAGxB", "debugId": null}}]}